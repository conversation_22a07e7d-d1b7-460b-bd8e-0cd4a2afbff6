(()=>{var zi=Object.create;var he=Object.defineProperty;var Ri=Object.getOwnPropertyDescriptor;var $i=Object.getOwnPropertyNames;var qi=Object.getPrototypeOf,Di=Object.prototype.hasOwnProperty;var Fi=t=>he(t,"__esModule",{value:!0});var Bi=(t,e)=>()=>(e||t((e={exports:{}}).exports,e),e.exports),Ze=(t,e)=>{for(var n in e)he(t,n,{get:e[n],enumerable:!0})},_i=(t,e,n,o)=>{if(e&&typeof e=="object"||typeof e=="function")for(let r of $i(e))!Di.call(t,r)&&(n||r!=="default")&&he(t,r,{get:()=>e[r],enumerable:!(o=Ri(e,r))||o.enumerable});return t},Wi=(t,e)=>_i(Fi(he(t!=null?zi(qi(t)):{},"default",!e&&t&&t.__esModule?{get:()=>t.default,enumerable:!0}:{value:t,enumerable:!0})),t);var To=Bi(()=>{Array.prototype.flat||Object.defineProperty(Array.prototype,"flat",{configurable:!0,value:function t(){var e=isNaN(arguments[0])?1:Number(arguments[0]);return e?Array.prototype.reduce.call(this,function(n,o){return Array.isArray(o)?n.push.apply(n,t.call(o,e-1)):n.push(o),n},[]):Array.prototype.slice.call(this)},writable:!0}),Array.prototype.flatMap||Object.defineProperty(Array.prototype,"flatMap",{configurable:!0,value:function(t){return Array.prototype.map.apply(this,arguments).flat()},writable:!0})});var Ln={};Ze(Ln,{attach:()=>T,debug:()=>Xt,defineRivetArchetype:()=>st,drive:()=>ee,registerAction:()=>wt,registerBehavior:()=>xt,registerEvent:()=>U,registerInnate:()=>te,registerMacro:()=>kn,registerObserver:()=>et,store:()=>Z,util:()=>S});var S={};Ze(S,{NON_PASSIVE_ARGS:()=>Vt,PASSIVE_ARGS:()=>N,SCROLLBAR_ACTIVE_VAR_NAME:()=>Jt,SCROLLBAR_ACTIVE_VAR_STRING:()=>yn,SCROLLBAR_VAR_NAME:()=>gn,SCROLLBAR_VAR_STRING:()=>Js,absVector:()=>Xi,addClass:()=>vs,addVectors:()=>Zi,animateToElement:()=>Ks,animateTopOffset:()=>Zs,appendHtmlString:()=>Ss,balanceFromDifference:()=>is,ceil:()=>Gi,clamp:()=>Bt,clampVector:()=>Ji,debounce:()=>At,defer:()=>ss,dispatch:()=>Rs,distanceInRange:()=>Xe,divideVectors:()=>os,doOnce:()=>Nt,elementAttributes:()=>Ct,elementIndex:()=>bs,elementIsVisibleInViewport:()=>Po,elementMeta:()=>ln,ensureNumber:()=>X,ensureString:()=>Ft,evaluateCondition:()=>la,expandElementValue:()=>yt,farthest:()=>As,floor:()=>Ui,fontCompress:()=>Ns,forceOpaque:()=>na,getCachedAttribute:()=>kt,getCachedJsonAttribute:()=>qs,getDurations:()=>an,getEasing:()=>mt,getJsonAttrContent:()=>on,getOuterHeight:()=>Cs,getPrecisionLength:()=>Yi,getPrecisionLengthWithCommas:()=>ji,getStateValue:()=>Ro,getTransitionDuration:()=>Os,getTransitionTimingMS:()=>ks,hasClass:()=>Wt,intersect:()=>Qs,isMobile:()=>pn,isScalar:()=>Ni,lerp:()=>rs,listener:()=>tt,listenerPassive:()=>q,lockMotion:()=>Ho,makeAlternatingSynchronizer:()=>Ls,makeDirectionalEasing:()=>ys,makeElementWeakMap:()=>Ut,makeFindClosest:()=>sn,makeGetComputedFloatValues:()=>Ws,makeGetComputedStyle:()=>_s,makeGetComputedStyles:()=>dn,makeMotionLockedUpdate:()=>zo,makeRafLoop:()=>we,makeSortByKey:()=>Vi,makeStateSynchronizer:()=>xo,makeTreeWalker:()=>ye,memoize:()=>en,multiplyVectors:()=>ns,normalizeCondition:()=>xn,normalizeTeardown:()=>Gt,offsetFromTop:()=>ve,onLoad:()=>V,onPageVisibilityChange:()=>cn,onPercentScrolled:()=>Fs,onResize:()=>Yt,onResizeOrScan:()=>lt,onScan:()=>xe,onScanLazy:()=>Bs,onScroll:()=>jt,onScrollOrResize:()=>Co,onScrollRaw:()=>un,onViewportChange:()=>ko,once:()=>Eo,oncePassive:()=>Et,parseHTML:()=>rn,parseTime:()=>gt,removeClass:()=>ws,rivetDispatch:()=>zs,rivetListener:()=>Hs,round:()=>ho,roundVector:()=>ts,runAnimation:()=>oa,scrollOffset:()=>Es,scrollingDisable:()=>Xs,scrollingEnable:()=>ta,setRootVar:()=>Zt,siblings:()=>wo,subtractVectors:()=>es,teardown:()=>j,throttle:()=>tn,toggleClass:()=>xs,transitionEnd:()=>$s,triggerScan:()=>Ds,tween:()=>Ms,unwrapHtmlTemplate:()=>Ts,updateStateKey:()=>aa,vectorsEq:()=>Ki,watchElementIsVisible:()=>ea,waypoint:()=>Gs,wrapNumber:()=>Ke,wrapVector:()=>Qi});function X(t){if(typeof t=="function")return X(t());if(typeof t=="number")return t;let e=Number.parseFloat(t);return Number.isNaN(e)?0:e}var Ft=t=>typeof t=="function"?Ft(t()):typeof t=="string"?t:"";function Ni(t){var e=typeof t;return e==="string"||e==="number"||e==="boolean"||e==="symbol"||t==null||t instanceof Symbol||t instanceof String||t instanceof Number||t instanceof Boolean}var Vi=t=>(e,n)=>e[t]-n[t];var Gi=(t,e=100)=>Math.ceil(t*e)/e,Ui=(t,e=100)=>Math.floor(t*e)/e,ho=(t,e=100)=>Math.round((t+Number.EPSILON)*e)/e,Yi=t=>{var e,n;return(n=(e=t.toString().split(/\./)[1])==null?void 0:e.length)!=null?n:0},ji=t=>{var e,n;return(n=(e=t.toString().split(/,/)[1])==null?void 0:e.length)!=null?n:0},Ke=(t,e)=>(e+t)%e,Qi=([t,e],[n,o])=>[Ke(t,n),Ke([e,o])],Bt=(t,e,n)=>Math.min(Math.max(t,e),n),Ji=([t,e],n,o)=>[Bt(t,n,o),Bt(e,n,o)],Zi=([t,e],[n,o])=>[t+n,e+o],Ki=([t,e],[n,o])=>t===n&&e===o,Xi=t=>t.map(Math.abs),ts=(t,e)=>t.map(n=>ho(n,e)),es=([t,e],[n,o])=>[t-n,e-o],ns=([t,e],[n,o])=>[t*n,e*o],os=([t,e],[n,o])=>[t/n,e/o],Xe=(t,e,n)=>(t-e+n)%n,rs=(t,e,n)=>t+n*(e-t),is=(t,e,n)=>{let o=Xe(t,e,n),r=Xe(e,t,n);return o===r?0:o>r?-1:1};function ss(t){return setTimeout(t,0)}function tn(t,e,n={}){var o=!0,r=!0;return o="leading"in n?!!n.leading:o,r="trailing"in n?!!n.trailing:r,At(t,e,{leading:o,maxWait:e,trailing:r})}function At(t,e=0,n={}){var o,r,i,s,a,c,u=0,d=!1,f=!1,p=!0;d=!!n.leading,f="maxWait"in n,i=f?Math.max(n.maxWait||0,e):i,p="trailing"in n?!!n.trailing:p;function l(x){var E=o,P=r;return o=r=void 0,u=x,s=t.apply(P,E),s}function h(x){return u=x,a=setTimeout(y,e),d?l(x):s}function m(x){var E=x-c,P=x-u,$=e-E;return f?Math.min($,i-P):$}function g(x){var E=x-c,P=x-u;return c===void 0||E>=e||E<0||f&&P>=i}function y(){var x=window.Date.now();if(g(x))return v(x);a=setTimeout(y,m(x))}function v(x){return a=void 0,p&&o?l(x):(o=r=void 0,s)}function w(){a!==void 0&&clearTimeout(a),u=0,o=c=r=a=void 0}function b(){return a===void 0?s:v(window.Date.now())}function C(){var x=window.Date.now(),E=g(x);if(o=arguments,r=this,c=x,E){if(a===void 0)return h(c),()=>void w();if(f)return clearTimeout(a),a=setTimeout(y,e),l(c),()=>void w()}return a===void 0&&(a=setTimeout(y,e)),()=>void w()}return C.cancel=w,C.flush=b,C}function en(t,e){let n=new Map;return function(...o){let r=e?e.apply(this,o):o[0];if(n.has(r))return n.get(r);let i=t.apply(this,o);return n.set(r,i),i}}var as=4,cs=.001,ls=1e-7,us=10,_t=11,me=1/(_t-1),fs=typeof Float32Array=="function";function mo(t,e){return 1-3*e+3*t}function go(t,e){return 3*e-6*t}function yo(t){return 3*t}function ge(t,e,n){return((mo(e,n)*t+go(e,n))*t+yo(e))*t}function vo(t,e,n){return 3*mo(e,n)*t*t+2*go(e,n)*t+yo(e)}function ds(t,e,n,o,r){var i,s,a=0;do s=e+(n-e)/2,i=ge(s,o,r)-t,i>0?n=s:e=s;while(Math.abs(i)>ls&&++a<us);return s}function ps(t,e,n,o){for(var r=0;r<as;++r){var i=vo(e,n,o);if(i===0)return e;var s=ge(e,n,o)-t;e-=s/i}return e}function hs(t){return t}function nn(t,e,n,o){if(!(0<=t&&t<=1&&0<=n&&n<=1))throw new Error("bezier x values must be in [0, 1] range");if(t===e&&n===o)return hs;for(var r=fs?new Float32Array(_t):new Array(_t),i=0;i<_t;++i)r[i]=ge(i*me,t,n);function s(a){for(var c=0,u=1,d=_t-1;u!==d&&r[u]<=a;++u)c+=me;--u;var f=(a-r[u])/(r[u+1]-r[u]),p=c+f*me,l=vo(p,t,n);return l>=cs?ps(a,p,t,n):l===0?p:ds(a,c,c+me,t,n)}return function(c){return c===0||c===1?c:ge(s(c),e,o)}}var ms=t=>{switch(t){case"linear":return"cubic-bezier(0.0, 0.0, 1.0, 1.0)";case"ease-in":return"cubic-bezier(0.42, 0, 1.0, 1.0)";case"ease-out":return"cubic-bezier(0, 0, 0.58, 1.0)";case"ease-in-out":return"cubic-bezier(0.42, 0, 0.58, 1.0)";case"ease":default:return"cubic-bezier(0.25, 0.1, 0.25, 1.0)"}},O=en(t=>{let e=ms(t);try{let[,n]=e.match(/cubic-bezier\((.*)\)/);return nn(...n.split(",").map(o=>Number(o.trim())))}catch{console.warn("unable to parse easing function",e)}return O("ease")}),gs={easeInQuad:O("cubic-bezier(0.550, 0.085, 0.680, 0.530)"),easeInCubic:O("cubic-bezier(0.550, 0.055, 0.675, 0.190)"),easeInQuart:O("cubic-bezier(0.895, 0.030, 0.685, 0.220)"),easeInQuint:O("cubic-bezier(0.755, 0.050, 0.855, 0.060)"),easeInSine:O("cubic-bezier(0.470, 0.000, 0.745, 0.715)"),easeInExpo:O("cubic-bezier(0.950, 0.050, 0.795, 0.035)"),easeInCirc:O("cubic-bezier(0.600, 0.040, 0.980, 0.335)"),easeInBack:O("cubic-bezier(0.600, -0.280, 0.735, 0.045)"),easeOutQuad:O("cubic-bezier(0.250, 0.460, 0.450, 0.940)"),easeOutCubic:O("cubic-bezier(0.215, 0.610, 0.355, 1.000)"),easeOutQuart:O("cubic-bezier(0.165, 0.840, 0.440, 1.000)"),easeOutQuint:O("cubic-bezier(0.230, 1.000, 0.320, 1.000)"),easeOutSine:O("cubic-bezier(0.390, 0.575, 0.565, 1.000)"),easeOutExpo:O("cubic-bezier(0.190, 1.000, 0.220, 1.000)"),easeOutCirc:O("cubic-bezier(0.075, 0.820, 0.165, 1.000)"),easeOutBack:O("cubic-bezier(0.175, 0.885, 0.320, 1.275)"),easeInOutQuad:O("cubic-bezier(0.455, 0.030, 0.515, 0.955)"),easeInOutCubic:O("cubic-bezier(0.645, 0.045, 0.355, 1.000)"),easeInOutQuart:O("cubic-bezier(0.770, 0.000, 0.175, 1.000)"),easeInOutQuint:O("cubic-bezier(0.860, 0.000, 0.070, 1.000)"),easeInOutSine:O("cubic-bezier(0.445, 0.050, 0.550, 0.950)"),easeInOutExpo:O("cubic-bezier(1.000, 0.000, 0.000, 1.000)"),easeInOutCirc:O("cubic-bezier(0.785, 0.135, 0.150, 0.860)"),easeInOutBack:O("cubic-bezier(0.680, -0.550, 0.265, 1.550)"),materialStand:O("cubic-bezier(0.400, 0.000, 0.200, 1.000)"),materialDecel:O("cubic-bezier(0.000, 0.000, 0.200, 1.000)"),materialAccel:O("cubic-bezier(0.400, 0.000, 1.000, 1.000)"),materialSharp:O("cubic-bezier(0.400, 0.000, 0.600, 1.000)")};function mt(t){return gs[t]||O(t)}var ys=t=>{let e=mt(t);return n=>{let o=(-1*n+1)/2,r=Math.min(1,Math.max(0,o));return(e(r)-.5)*2}};var Wt=(t,e)=>{var n;return(n=t==null?void 0:t.classList)==null?void 0:n.contains(e)},vs=(t,e)=>{var n;return(n=t==null?void 0:t.classList)==null?void 0:n.add(e)},ws=(t,e)=>{var n;return(n=t==null?void 0:t.classList)==null?void 0:n.remove(e)},xs=(t,e,n)=>{var o;return(o=t==null?void 0:t.classList)==null?void 0:o.toggle(e,n)};function bs(t){if(!t)return-1;for(var e=0;t=t.previousElementSibling;)e++;return e}function on(t,e){let n=t.getAttribute(e);if(n===null)return{};if(typeof n=="string")try{return JSON.parse(n)}catch{try{return JSON.parse(n.replace(/&quot;/g,'"'))}catch{}}return n}var rn=t=>{let e=document.implementation.createHTMLDocument("");return e.body.innerHTML=t,e.body.children},Ss=(t,e)=>{Array.from(rn(e)).forEach(n=>{t.append(n)})},Ts=t=>{Array.from(rn(t.innerHTML)).forEach(e=>{t.insertAdjacentElement("afterend",e)}),t.remove()};function As(t,e){let n=t,o;for(;n&&n.parentElement;)n=n.parentElement.closest(e),n&&(o=n);return o}var wo=t=>t&&t.parentElement?Array.from(t.parentElement.children).filter(e=>e!==t):[],ye=(t,e)=>n=>{let o=new Set,r=document.createTreeWalker(n,NodeFilter.SHOW_ELEMENT,{acceptNode:()=>NodeFilter.FILTER_ACCEPT});for(;r.nextNode();)if(t(r.currentNode)){if(e)return r.currentNode;o.add(r.currentNode)}return e?null:Array.from(o)},ve=t=>t?t.offsetParent?t.offsetTop+ve(t.offsetParent):t.offsetTop:0,Es=t=>{let e=t.getBoundingClientRect(),{top:n,left:o,height:r}=e;return{top:n+window.scrollY,bottom:n+r+window.scrollY,left:o+window.scrollX}},Cs=t=>{var r;if(!t)return 0;let e=Math.max(t.scrollHeight,t.offsetHeight),n=(r=t.getAttribute("style"))!=null?r:"";t.style.display="block",t.style.position="absolute",t.style.visibility="hidden";let o=Math.max(0,e,t.scrollHeight,t.offsetHeight);return t.setAttribute("style",n),o},sn=t=>{let e=ye(t,!0),n=r=>{let i=r;for(;i;){if(t(i))return i;i=i.parentElement}},o=r=>{let i=r;for(;i;){let s;if(wo(i).find(a=>(s=t(a)?a:e(a),s)),s)return s;i=i.parentElement}};return r=>n(r)||o(r)||null};function ks(t){if(!t)return 0;let n=window.getComputedStyle(t)["transition-duration"]||"";return parseFloat(n.replace("s",""))*1e3}var xo=(t,{pending:e=()=>{},delay:n=10,initialState:o=null}={})=>{let r=o,i=o,s=[],a=!1,c=()=>{r!==i&&(a=!0,e(!0),r=i,t(r,(...d)=>{a=!1,e(!1),s=d,c()},...s))},u=At(d=>{i=d,a||c()},n);return u.reset=()=>{r=!1,i=!1,s=[]},u},Ls=(t,e,n,o=!1)=>xo((r,i,s)=>{r?t(()=>void i(e)):e(i),s&&s(i)},{delay:n,initialState:o}),Nt=t=>{let e=!1;return(...n)=>{if(!e)return e=!0,t(...n)}},gt=(t,e=0)=>{if(typeof t=="number")return t;let n=typeof t=="string"?t:"",[,o,r=""]=n.match(/(\d*.?\d+)(\w*)/)||[],i=parseFloat(o);return Number.isNaN(i)?e:r.toLowerCase()==="s"?i*1e3:i};function Os(t,e){return gt(t&&window.getComputedStyle(t).getPropertyValue("transition-duration"),e)}function an(t){let e=window.getComputedStyle(t);e.getPropertyValue("transition-duration");let n=gt(e.getPropertyValue("transition-duration"),0),o=gt(e.getPropertyValue("transition-delay"),0),r=gt(e.getPropertyValue("animation-duration"),0),i=gt(e.getPropertyValue("animation-delay"),0);return{transitionDuration:n,transitionDelay:o,animationDuration:r,animationDelay:i,transitionTime:n+o,animationTime:r+i}}var we=t=>{let e,n,o=r=>{typeof e=="undefined"&&(e=r);let i=r-e;t(i,r)!==!1&&(n=requestAnimationFrame(o))};return n=requestAnimationFrame(o),()=>void cancelAnimationFrame(n)},Is=({setup:t=()=>{},update:e=()=>{},complete:n=()=>{},cancel:o=()=>{},duration:r,easing:i})=>{let s=gt(r,500),a=mt(i);t();let c=we(u=>{if(u<s)e(a(u/s));else return e(1),n(),!1});return()=>{o(),c()}},Ps=(t,e,n)=>e===n?n:n>e?e+(n-e)*t:e+(e-n)*(t*-1),bo=t=>Object.keys(t).reduce((e,n)=>(e[n]=parseFloat(t[n]),e),{}),So=(t,{update:e,interpolate:n=Ps,...o})=>{let r=bo(t);return(i={})=>{let s=bo(i);return Is({update:a=>{e(Object.keys(s).reduce((c,u)=>(c[u]=n(a,r[u],s[u]),c),{}))},...o})}},Ms=(t,e)=>{let n=typeof t=="object"?So(t,e):So({from:t},{...e,update:({from:o})=>e.update(o)});return o=>n(typeof o=="object"?o:{from:o})};Promise.resolve().then(()=>Wi(To()));var Ao="rvt",N={passive:!0},Vt={passive:!1};function Hs(t,e,n){return tt(t,`${Ao}-${e}`,n)}function zs(t,e){t.dispatchEvent(new CustomEvent(`${Ao}-${e}`))}function Rs(t,e,n={},o=!0){t.dispatchEvent(new CustomEvent(e),{bubbles:o,detail:n})}function tt(t,e,n,o={}){return t?(typeof o.passive=="undefined"&&(o.passive=!1),t.addEventListener(e,n,o),()=>void t.removeEventListener(e,n,o)):()=>{}}function q(t,e,n){return tt(t,e,n,N)}function V(t){let e=()=>void t();return document.readyState==="complete"?(e(),()=>{}):q(document,"readystatechange",function(){document.readyState==="complete"&&setTimeout(e,0)})}function Eo(t,e,n,o=Vt){let r=function(i){t.removeEventListener(e,r),n(i)};return t.addEventListener(e,r,o),()=>void t.removeEventListener(e,r)}function Et(t,e,n){return Eo(t,e,n,N)}var Gt=t=>(Array.isArray(t)?t.map(Gt):[t]).flat().filter(e=>typeof e=="function"),j=t=>{let e=Gt(t);return()=>e.forEach(n=>n())};function $s(t,e){let o=window.getComputedStyle(t)["transition-duration"];if(o=o?parseFloat(o.replace("s","")):0,o===0){e();return}let r=Nt(e),i=setTimeout(function(){r()},o*1e3+500),s=Et(t,"transitionend",r);return function(){clearTimeout(i),s()}}var cn=(t,e)=>(e&&t(document.visibilityState==="visible"),j([tt(window,"pagehide",()=>{t(!1)}),tt(window.document,"visibilitychange",()=>{t(document.visibilityState==="visible")})]));function _(t,e){let n,o=null;return function(r){if(n){o=r;return}n=setTimeout(function(){t(o),n=null},e)}}var Ut=(t={})=>{let e,n=()=>{e=new WeakMap},o=c=>e.has(c),r=c=>e.delete(c),i=c=>e.has(c)?e.get(c):t,s=(c,u)=>void e.set(c,u),a=(c,u)=>void s(c,u(i(c)));return n(),{get:i,del:r,set:s,has:o,update:a,reset:n,cache:()=>e}},ln=Ut(),Ct=Ut();function kt(t,e){return Ct.has(t)||Ct.set(t,new Map),Ct.get(t).has(e)||Ct.get(t).set(e,on(t,e)),Ct.get(t).get(e)}function qs(t,e){if(!t)return{};let n=kt(t,e);return typeof n=="object"?n:{}}var Ds=()=>window.dispatchEvent(new CustomEvent("rvt-scan")),xe=t=>tt(window,"rvt-scan",()=>t()),Yt=(t,e=!1)=>{e&&t();let n=_(t,100);return j([q(window,"resize",n,N),tt(screen.orientation,"change",n)])},jt=(t,e=!1)=>{e&&t();let n=_(t,40);return q(window,"scroll",n)},un=(t,e=!1)=>(e&&t(),q(window,"scroll",t)),Co=(t,e=!1)=>j([jt(t,e),lt(t,e)]),lt=(t,e)=>j([xe(t),Yt(t,e)]),ko=(t,e)=>j([xe(t),V(t),cn(t,!1),Yt(t,e)]),Fs=(t,e,n=!1)=>{let o,r,i=Co(()=>{let s=document.body.offsetHeight,c=1-(s-(window.scrollY+window.innerHeight))/s>=t;c!==r&&(e(c),c&&n&&(o=!0,i()),r=c)},!0);return()=>{o||i()}},Bs=(t,{throttle:e=50}={})=>{let n,r=tn(()=>{n=requestAnimationFrame(()=>void t())},e,{trailing:!0}),i=At(r,450);return[V(i),Yt(i),xe(r),()=>cancelAnimationFrame(n)]},fn,Lo;ko(()=>{fn=new WeakMap,Lo=new WeakMap},!0);jt(()=>{Lo=new WeakMap},!0);var Oo=t=>{let e=fn.get(t);return e||(e=new WeakMap,fn.set(t,e)),e};function dn(t,e){let n=function(o){let r=Oo(n).get(o);if(!r){let i=getComputedStyle(o);r=t.reduce((s,a)=>(s[a]=typeof e=="function"?e(i[a],a):i[a],s),{}),Oo(n).set(o,r)}return r};return n}function _s(t){let e=dn([t]);return n=>e(n)[t]}function Ws(t){return dn(t,e=>parseFloat(e))}function Ns(t,{c:e=1,min:n=Number.NEGATIVE_INFINITY,max:o=Number.POSITIVE_INFINITY}){let r=X(n),i=X(o);return Yt(()=>{let s=Bt(parseFloat(getComputedStyle(t,null).width)/(e*10),r,i);t.style.setProperty("font-size",`${s}px`)},!0)}var Vs="ontouchstart"in document.documentElement;function pn(){return window.innerWidth<=978.98&&Vs}var Io=0,Lt={};function A(t){if(!t)throw new Error("No options passed to Waypoint constructor");if(!t.element)throw new Error("No element option passed to Waypoint constructor");if(!t.handler)throw new Error("No handler option passed to Waypoint constructor");this.key="waypoint-"+Io,this.options=A.Adapter.extend({},A.defaults,t),this.element=this.options.element,this.adapter=new A.Adapter(this.element),this.callback=t.handler,this.axis=this.options.horizontal?"horizontal":"vertical",this.enabled=this.options.enabled,this.triggerPoint=null,this.group=A.Group.findOrCreate({name:this.options.group,axis:this.axis}),this.context=A.Context.findOrCreateByElement(this.options.context),A.offsetAliases[this.options.offset]&&(this.options.offset=A.offsetAliases[this.options.offset]),this.group.add(this),this.context.add(this),Lt[this.key]=this,Io+=1}A.prototype.queueTrigger=function(t){this.group.queueTrigger(this,t)};A.prototype.trigger=function(t){!this.enabled||this.callback&&this.callback.apply(this,t)};A.prototype.destroy=function(){this.context.remove(this),this.group.remove(this),delete Lt[this.key]};A.prototype.disable=function(){return this.enabled=!1,this};A.prototype.enable=function(){return this.context.refresh(),this.enabled=!0,this};A.prototype.next=function(){return this.group.next(this)};A.prototype.previous=function(){return this.group.previous(this)};A.invokeAll=function(t){var e=[];for(var n in Lt)e.push(Lt[n]);for(var o=0,r=e.length;o<r;o++)e[o][t]()};A.destroyAll=function(){A.invokeAll("destroy")};A.disableAll=function(){A.invokeAll("disable")};A.enableAll=function(){A.Context.refreshAll();for(var t in Lt)Lt[t].enabled=!0;return this};A.refreshAll=function(){A.Context.refreshAll()};A.viewportHeight=function(){return window.innerHeight||document.documentElement.clientHeight};A.viewportWidth=function(){return document.documentElement.clientWidth};A.adapters=[];A.defaults={context:window,continuous:!0,enabled:!0,group:"default",horizontal:!1,offset:0};A.offsetAliases={"bottom-in-view":function(){return this.context.innerHeight()-this.adapter.outerHeight()},"right-in-view":function(){return this.context.innerWidth()-this.adapter.outerWidth()}};(function(){"use strict";var t=0,e={},n=window.onload;function o(r){this.element=r,this.Adapter=A.Adapter,this.adapter=new this.Adapter(r),this.key="waypoint-context-"+t,this.didScroll=!1,this.didResize=!1,this.oldScroll={x:this.adapter.scrollLeft(),y:this.adapter.scrollTop()},this.waypoints={vertical:{},horizontal:{}},r.waypointContextKey=this.key,e[r.waypointContextKey]=this,t+=1,A.windowContext||(A.windowContext=!0,A.windowContext=new o(window)),this.createThrottledScrollHandler(),this.createThrottledResizeHandler()}o.prototype.add=function(r){var i=r.options.horizontal?"horizontal":"vertical";this.waypoints[i][r.key]=r,this.refresh()},o.prototype.checkEmpty=function(){var r=this.Adapter.isEmptyObject(this.waypoints.horizontal),i=this.Adapter.isEmptyObject(this.waypoints.vertical),s=this.element==this.element.window;r&&i&&!s&&(this.adapter.off(".waypoints"),delete e[this.key])},o.prototype.createThrottledResizeHandler=function(){var r=this;function i(){r.handleResize(),r.didResize=!1}this.adapter.on("resize.waypoints",function(){r.didResize||(r.didResize=!0,requestAnimationFrame(i))})},o.prototype.createThrottledScrollHandler=function(){var r=this;function i(){r.handleScroll(),r.didScroll=!1}this.adapter.on("scroll.waypoints",function(){(!r.didScroll||A.isTouch)&&(r.didScroll=!0,requestAnimationFrame(i))})},o.prototype.handleResize=function(){A.Context.refreshAll()},o.prototype.handleScroll=function(){var r={},i={horizontal:{newScroll:this.adapter.scrollLeft(),oldScroll:this.oldScroll.x,forward:"right",backward:"left"},vertical:{newScroll:this.adapter.scrollTop(),oldScroll:this.oldScroll.y,forward:"down",backward:"up"}};for(var s in i){var a=i[s],c=a.newScroll>a.oldScroll,u=c?a.forward:a.backward;for(var d in this.waypoints[s]){var f=this.waypoints[s][d];if(f.triggerPoint!==null){var p=a.oldScroll<f.triggerPoint,l=a.newScroll>=f.triggerPoint,h=p&&l,m=!p&&!l;(h||m)&&(f.queueTrigger(u),r[f.group.id]=f.group)}}}for(var g in r)r[g].flushTriggers();this.oldScroll={x:i.horizontal.newScroll,y:i.vertical.newScroll}},o.prototype.innerHeight=function(){return this.element==this.element.window?A.viewportHeight():this.adapter.innerHeight()},o.prototype.remove=function(r){delete this.waypoints[r.axis][r.key],this.checkEmpty()},o.prototype.innerWidth=function(){return this.element==this.element.window?A.viewportWidth():this.adapter.innerWidth()},o.prototype.destroy=function(){var r=[];for(var i in this.waypoints)for(var s in this.waypoints[i])r.push(this.waypoints[i][s]);for(var a=0,c=r.length;a<c;a++)r[a].destroy()},o.prototype.refresh=function(){var r=this.element==this.element.window,i=r?void 0:this.adapter.offset(),s={},a;this.handleScroll(),a={horizontal:{contextOffset:r?0:i.left,contextScroll:r?0:this.oldScroll.x,contextDimension:this.innerWidth(),oldScroll:this.oldScroll.x,forward:"right",backward:"left",offsetProp:"left"},vertical:{contextOffset:r?0:i.top,contextScroll:r?0:this.oldScroll.y,contextDimension:this.innerHeight(),oldScroll:this.oldScroll.y,forward:"down",backward:"up",offsetProp:"top"}};for(var c in a){var u=a[c];for(var d in this.waypoints[c]){var f=this.waypoints[c][d],p=f.options.offset,l=f.triggerPoint,h=0,m=l==null,g,y,v,w,b;f.element!==f.element.window&&(h=f.adapter.offset()[u.offsetProp]),typeof p=="function"?p=p.apply(f):typeof p=="string"&&(p=parseFloat(p),f.options.offset.indexOf("%")>-1&&(p=Math.ceil(u.contextDimension*p/100))),g=u.contextScroll-u.contextOffset,f.triggerPoint=Math.floor(h+g-p),y=l<u.oldScroll,v=f.triggerPoint>=u.oldScroll,w=y&&v,b=!y&&!v,!m&&w?(f.queueTrigger(u.backward),s[f.group.id]=f.group):(!m&&b||m&&u.oldScroll>=f.triggerPoint)&&(f.queueTrigger(u.forward),s[f.group.id]=f.group)}}return requestAnimationFrame(function(){for(var C in s)s[C].flushTriggers()}),this},o.findOrCreateByElement=function(r){return o.findByElement(r)||new o(r)},o.refreshAll=function(){for(var r in e)e[r].refresh()},o.findByElement=function(r){return e[r.waypointContextKey]},window.onload=function(){n&&n(),o.refreshAll()},A.Context=o})();(function(){"use strict";function t(r,i){return r.triggerPoint-i.triggerPoint}function e(r,i){return i.triggerPoint-r.triggerPoint}var n={vertical:{},horizontal:{}};function o(r){this.name=r.name,this.axis=r.axis,this.id=this.name+"-"+this.axis,this.waypoints=[],this.clearTriggerQueues(),n[this.axis][this.name]=this}o.prototype.add=function(r){this.waypoints.push(r)},o.prototype.clearTriggerQueues=function(){this.triggerQueues={up:[],down:[],left:[],right:[]}},o.prototype.flushTriggers=function(){for(var r in this.triggerQueues){var i=this.triggerQueues[r],s=r==="up"||r==="left";i.sort(s?e:t);for(var a=0,c=i.length;a<c;a+=1){var u=i[a];(u.options.continuous||a===i.length-1)&&u.trigger([r])}}this.clearTriggerQueues()},o.prototype.next=function(r){this.waypoints.sort(t);var i=A.Adapter.inArray(r,this.waypoints),s=i===this.waypoints.length-1;return s?null:this.waypoints[i+1]},o.prototype.previous=function(r){this.waypoints.sort(t);var i=A.Adapter.inArray(r,this.waypoints);return i?this.waypoints[i-1]:null},o.prototype.queueTrigger=function(r,i){this.triggerQueues[i].push(r)},o.prototype.remove=function(r){var i=A.Adapter.inArray(r,this.waypoints);i>-1&&this.waypoints.splice(i,1)},o.prototype.first=function(){return this.waypoints[0]},o.prototype.last=function(){return this.waypoints[this.waypoints.length-1]},o.findOrCreate=function(r){return n[r.axis][r.name]||new o(r)},A.Group=o})();(function(){"use strict";function t(o){return o===o.window}function e(o){return t(o)?o:o.defaultView}function n(o){this.element=o,this.handlers={}}n.prototype.innerHeight=function(){var o=t(this.element);return o?this.element.innerHeight:this.element.clientHeight},n.prototype.innerWidth=function(){var o=t(this.element);return o?this.element.innerWidth:this.element.clientWidth},n.prototype.off=function(o,r){function i(p,l,h){for(var m=0,g=l.length-1;m<g;m++){var y=l[m];(!h||h===y)&&p.removeEventListener(y)}}var s=o.split("."),a=s[0],c=s[1],u=this.element;if(c&&this.handlers[c]&&a)i(u,this.handlers[c][a],r),this.handlers[c][a]=[];else if(a)for(var d in this.handlers)i(u,this.handlers[d][a]||[],r),this.handlers[d][a]=[];else if(c&&this.handlers[c]){for(var f in this.handlers[c])i(u,this.handlers[c][f],r);this.handlers[c]={}}},n.prototype.offset=function(){if(!this.element.ownerDocument)return null;var o=this.element.ownerDocument.documentElement,r=e(this.element.ownerDocument),i={top:0,left:0};return this.element.getBoundingClientRect&&(i=this.element.getBoundingClientRect()),{top:i.top+r.pageYOffset-o.clientTop,left:i.left+r.pageXOffset-o.clientLeft}},n.prototype.on=function(o,r){var i=o.split("."),s=i[0],a=i[1]||"__default",c=this.handlers[a]=this.handlers[a]||{},u=c[s]=c[s]||[];u.push(r),this.element.addEventListener(s,r)},n.prototype.outerHeight=function(o){var r=this.innerHeight(),i;return o&&!t(this.element)&&(i=window.getComputedStyle(this.element),r+=parseInt(i.marginTop,10),r+=parseInt(i.marginBottom,10)),r},n.prototype.outerWidth=function(o){var r=this.innerWidth(),i;return o&&!t(this.element)&&(i=window.getComputedStyle(this.element),r+=parseInt(i.marginLeft,10),r+=parseInt(i.marginRight,10)),r},n.prototype.scrollLeft=function(){var o=e(this.element);return o?o.pageXOffset:this.element.scrollLeft},n.prototype.scrollTop=function(){var o=e(this.element);return o?o.pageYOffset:this.element.scrollTop},n.extend=function(){var o=Array.prototype.slice.call(arguments);function r(a,c){if(typeof a=="object"&&typeof c=="object")for(var u in c)c.hasOwnProperty(u)&&(a[u]=c[u]);return a}for(var i=1,s=o.length;i<s;i++)r(o[0],o[i]);return o[0]},n.inArray=function(o,r,i){return r==null?-1:r.indexOf(o,i)},n.isEmptyObject=function(o){for(var r in o)return!1;return!0},A.adapters.push({name:"noframework",Adapter:n}),A.Adapter=n})();var hn=A;q(window,"rvt-scan",hn.Context.refreshAll);function Gs(t,e,n,o=!0){let r=new hn({element:t,handler:(...s)=>{e(...s),o&&i()},offset:n}),i=()=>r&&void r.destroy();return i}var Qt=new Map,Us=({threshold:t=.5,top:e="0px",bottom:n="0px"}={})=>{let o=Number.parseFloat(t);return{key:`${e}:${n}:${o}`,options:{root:null,rootMargin:`${e} 0px ${n} 0px`,_threshold:o}}},Ys=(t,e)=>new IntersectionObserver((n,o)=>{let{subscribers:r}=Qt.get(t);n.forEach(i=>{let s=r.get(i.target);if(s)for(let a of s.values())a(i)})},e),js=t=>{let{key:e,options:n}=Us(t);return Qt.has(e)||Qt.set(e,{observer:Ys(e,n),subscribers:new Map,key:e}),Qt.get(e)},Qs=(t,e,n)=>{if(typeof window.IntersectionObserver=="undefined")return function(){};let{observer:o,subscribers:r,key:i}=js(n);return r.has(t)||(r.set(t,new Set),o.observe(t)),r.get(t).add(e),()=>{r.get(t).delete(e),r.get(t).size<=0&&(r.delete(t),o.unobserve(t)),r.size<=0&&(o.disconnect(),Qt.delete(i))}};var mn=0,gn="--x-body-scroll-bar-size",Js=`var(${gn}, 0)`,Jt="--x-body-scroll-active-bar-size",yn=`var(${Jt}, 0)`,Zs=(t=0,e=850,n=null,o=()=>{},r=window)=>{let i=X(typeof t=="function"?t(0):t),s=mt(n),a=X(e),c=r.scrollY||document.documentElement.scrollTop;return Mo(r,c,s,a,o,i)},Ks=(t,e=0,n=850,o=null,r=()=>{},i=window)=>{let s=mt(o),a=X(n),c=i.scrollY||document.documentElement.scrollTop;return Mo(i,c,s,a,r,function(){return ve(t)+X(typeof e=="function"?e(0):e)})},be="auto",Se=!1,Te=t=>{t.target&&t.target.closest&&(t.target.closest("[data-x-scrollbar]")||t.target.closest(".x-off-canvas")||t.target.closest(".x-modal"))||(t.preventDefault(),t.stopPropagation())},Xs=()=>{if(Se)return;Se=!0;let{adminBarOffset:t}=window.csGlobal;be=document.body.style.touchAction==="none"?be:document.body.style.touchAction,document.body.style.touchAction="none";let e=window.scrollY-t();document.body.style.top=-e+"px",document.body.classList.add("x-body-scroll-disabled"),window.addEventListener("wheel",Te,Vt),window.addEventListener("scroll",Te,Vt),Zt(Jt,mn+"px")},ta=()=>{if(!Se)return;Se=!1;let{adminBarOffset:t}=window.csGlobal;document.body.style.touchAction=be==="none"?"auto":be,document.body.classList.remove("x-body-scroll-disabled");let e=-(parseFloat(document.body.style.top)-t());document.body.style.top="",window.scrollTo({top:e}),setTimeout(function(){window.dispatchEvent(new CustomEvent("resize"))},250),window.removeEventListener("wheel",Te),window.removeEventListener("scroll",Te),Zt(Jt,"0px")},ea=(t,e=0,n=0,o)=>{let r=_(function(){o(Po(t,e,n))},25);return j([lt(r),jt(r)])},Po=(t,e=0,n=0)=>{e===0&&(e=.01),n===0&&(n=.01);let{top:o,left:r,bottom:i,right:s}=t.getBoundingClientRect(),{innerHeight:a,innerWidth:c}=window,u=e?a*(1-parseFloat(e)/100):0,d=n?a*(parseFloat(n)/100):a;return o<=u&&r>=0&&i>=d&&s<=c};V(function(){mn=window.innerWidth-document.body.offsetWidth,Zt(gn,mn+"px"),Zt(Jt,"0px")});function Zt(t,e){document.querySelector(":root").style.setProperty(t,e)}function Mo(t,e,n,o,r,i=null){let s=t===window;return we(a=>{let c=typeof i=="function"?i():i;if(a<o){let u=e+(c-e)*n(a/o);t.scrollTo(0,u),s&&document.body.scrollTo(0,u)}else return t.scrollTo(0,c),s&&document.body.scrollTo(0,c),r(),!1})}function Ho(t,e=null){return e?t.style.setProperty("transition-property",e,"important"):t.style.setProperty("transition","none","important"),t.style.setProperty("animation","none","important"),()=>{t.offsetHeight,t.style.removeProperty(e?"transition-property":"transition"),t.style.removeProperty("animation")}}var zo=(t,e)=>(n,{after:o}={})=>{t(n);let r=Ho(n);return()=>{e(n),r(),typeof o=="function"&&o()}},na=zo(t=>t.style.setProperty("opacity",1,"important"),t=>t.style.removeProperty("opacity")),oa=(t,{animation:e,className:n,timeout:o,remove:r},i=()=>{})=>{if(!e)return;n&&!t.classList.contains(n)&&t.classList.add(n),t.style.removeProperty("animation-duration"),t.style.setProperty("animation-name",e);let s=o?an(t).animationTime:0;t.csAnimationEndingTimeout&&clearTimeout(t.csAnimationEndingTimeout);let a=Nt(()=>{r&&(t.csAnimationEndingTimeout=setTimeout(function(){t.style.animationName===e&&t.style.setProperty("animation-name","")},250)),i()});Et(t,"animationend",a),o&&setTimeout(a,s)};function ra(t,e,n){e=typeof e=="number"?Ot(e.toString()):typeof e=="string"?Ot(e):e;let o=(r,i,s,a)=>{let c,u=i[a];if(i.length>a){if(Array.isArray(r))try{u=vn(u,r),c=r.slice()}catch(d){if(r.length===0)c={};else throw new Error(d)}else c=Object.assign({},r);return c[u]=o(r[u]!==void 0?r[u]:{},i,s,a+1),c}return typeof s=="function"?s(r):s};return o(t,e,n,0)}function ia(t,e,n){e=typeof e=="number"?Ot(e.toString()):typeof e=="string"?Ot(e):e;for(var o=0;o<e.length;o++){if(t===null||typeof t!="object")return n;let r=e[o];Array.isArray(t)&&r==="$end"&&(r=t.length-1),t=t[r]}return typeof t=="undefined"?n:t}function sa(t,e){e=typeof e=="number"?Ot(e.toString()):typeof e=="string"?Ot(e):e;let n=(o,r,i)=>{let s,a=r[i];return o===null||typeof o!="object"||!Array.isArray(o)&&o[a]===void 0?o:r.length-1>i?(Array.isArray(o)?(a=vn(a,o),s=o.slice()):s=Object.assign({},o),s[a]=n(o[a],r,i+1),s):(Array.isArray(o)?(a=vn(a,o),s=[].concat(o.slice(0,a),o.slice(a+1))):(s=Object.assign({},o),delete s[a]),s)};return n(t,e,0)}function vn(t,e){if(t==="$end"&&(t=Math.max(e.length-1,0)),!/^\+?\d+$/.test(t))throw new Error(`Array index '${t}' has to be an integer`);return parseInt(t)}function Ot(t){return t.split(".").reduce((e,n,o,r)=>{let i=o>0&&r[o-1];if(i&&/(?:^|[^\\])\\$/.test(i)){let s=e.pop();e.push(s.slice(0,-1)+"."+n)}else e.push(n);return e},[])}var wn={get:ia,set:ra,deleteProperty:sa};var Ro=(t,e)=>e&&typeof t=="object"?wn.get(t,Ft(e)):t,aa=(t,e,n)=>e&&typeof t=="object"?{...t,[Ft(e)]:n}:n;function yt(t,e){if(Array.isArray(e))return e.map(i=>yt(t,i));if(typeof e=="function")return yt(t,e(t));if(typeof e=="object")return Object.keys(e).reduce((i,s)=>(i[s]=yt(t,e[s]),i),{});if(typeof e!="string")return e;let n,o=()=>(n||(n=window.getComputedStyle(t)),n),r=ln.get(t);return e.replace(/var\(([\w-]+)(?:\s*,\s*(.+?))?\)/g,(i,s,a="")=>o().getPropertyValue(s)||a).replace(/attr\(([\w-]+)(?:\s*,\s*(.+?))?\)/g,(i,s,a="")=>t.getAttribute(s)||a).replace(/meta\(([.\w-]+)(?:\s*,\s*(.+?))?\)/g,(i,s,a="")=>{let c=wn.get(r,s);return typeof c=="undefined"?a:c})}var ca=(t,e,n)=>{let o=t,r=e;return n==="IN"?o.includes(r):n==="NOT IN"?!o.includes(r):((typeof o=="boolean"||typeof r=="boolean")&&(o=!!o,r=!!r),n===">"||n==="<"?o>r:n===">="||n==="<="?o>=r:n==="="||n==="=="?o==r:n==="!="?o!=r:n=="==="?o===r:n=="!=="?o!==r:!0)},xn=(t,e)=>{if(typeof t=="undefined")return null;if(!Array.isArray(t))return xn([t,"==",e]);let[n,o,r]=t;return t.length<=0?null:[n,o,r||e]},la=(t,e,n,o)=>{try{let r=xn(e,o);if(!r)return!0;let[i,s,a]=r;return ca(Ro(n,yt(t,a)),yt(t,i),yt(t,s))}catch(r){console.warn("Failed to check condition. Make sure your state contains the key you are checking",r,{el:t,condition:e,state:n})}return!1};var Kt=new Map,bn=new Map,Ae=(...t)=>void console.warn(...t),st=(t,e)=>{if(Kt.has(t))return Ae("Rivet archetypes can not be redefined");bn.set(t,typeof e=="function"?e:(n,...o)=>n(...o))},vt=(t,e,n,o={})=>{if(Kt.has(e))return Ae(`Rivet ${e} already registered`);if(!bn.has(t))return Ae("Rivet archetype unknown",t);Kt.set(e,{archetype:t,handler:n,config:o})};function Ee({el:t,handle:e,handles:n,defaultOption:o,options:r}){var l;let i=n||[e],s=i.find(({type:h})=>h&&Kt.has(h));if(!s)return Ae(`Unknown rivet type: ${(l=i[0])==null?void 0:l.type}`,t);let{handler:a,config:{defaultOption:c,priority:u=0,...d}={},archetype:f}=Kt.get(s.type),p=c&&o?{[c]:o}:{};return[u,()=>bn.get(f)(a,t,{...p,...r||{}},s.name,d)]}function Ce(t){return t.sort(([e],[n])=>e-n)}var Sn="data-rvt";function $o(t=window.document.body){return ye(Tn)(t)}function Tn(t){if(t.hasAttributes()){let e=t.attributes;for(let n=e.length-1;n>=0;n--)if(e[n].name.indexOf(Sn)===0)return!0}return!1}function ua(t,e){let n=kt(t,e);return typeof n!="object"&&typeof n!="undefined"&&n!==""?{defaultOption:n,options:{}}:{options:n}}function fa(t){let e=new Set;for(let n=t.attributes.length-1;n>=0;n--){let o=t.attributes[n].name;if(o.indexOf(Sn)!==0)continue;let r=o.substr(Sn.length+1),i=r.split("-");e.add({el:t,handles:[{type:r,name:"default"},{name:i.pop(),type:i.join("-")}],...ua(t,o)})}return e}function An(t){return j(Ce(Array.from(fa(t)).map(e=>Ee(e)).filter(e=>!!e)).map(([,e])=>e()))}function qo(t,e){try{return t&&t.matches&&t.matches(e)}catch{}return!1}function ke(t){try{return window.document.querySelectorAll(t)}catch(e){console.warn(e)}return[]}var Do=!1,Fo=null,Bo,En=new Set,Cn=new Set,Le=new WeakMap,It=new WeakMap;function T(t,e,n=0){if(typeof t!="string"){console.warn("Rivet selector must be a string",t),console.trace();return}if(typeof e!="function"){console.warn("Rivet handler must be a function",e),console.trace();return}let o={handler:e,selector:t,priority:n};En.add(o),Do&&da(o)}function da(t){clearTimeout(Bo),Cn.add(t),Bo=setTimeout(()=>{let e=Array.from(Cn.values());Cn.clear(),_o(e)},0)}function _o(t){t.sort(({priority:e},{priority:n})=>e-n),t.forEach(({selector:e,handler:n})=>{Array.from(ke(e)).forEach(o=>{Oe(o,n,Wo(e,o))})})}function Wo(t,e){let n=t.match(/(data-[\w-]+)/g)||[];return n&&n.length?n=n.pop():n=null,n?kt(e,n):null}function Oe(t,e,n){try{if(pa(t,e)||!document.body.contains(t))return;ha(t,e);let o=Gt(e.call(window,t,n));Array.isArray(o)&&o.map(r=>{No(t,r)}),typeof o=="function"&&No(t,o)}catch(o){console.warn("Failed to attach handler to element",t,e,n,o)}}function No(t,e){typeof e=="function"&&(Le.get(t)||Le.set(t,new Set),Le.get(t).add(e))}function pa(t,e){return It.get(t)&&It.get(t).get(e)}function ha(t,e){It.get(t)||It.set(t,new WeakMap),It.get(t).set(e,!0)}window.document.addEventListener("DOMContentLoaded",()=>{$o().forEach(t=>{Oe(t,e=>An(e))}),_o(Array.from(En.values())),Fo=new MutationObserver(function(t){t.reduce((e,n)=>{for(let o=0;o<n.addedNodes.length;o++)n.addedNodes[o].nodeType===1&&e.push(n.addedNodes[o]);return e},[]).forEach(function e(n){if(!!n){if(n.children&&n.children.length>0)for(let o=0;o<n.children.length;o++){if(!n)return;e(n.children[o])}Tn(n)&&Oe(n,o=>An(o)),En.forEach(({selector:o,handler:r})=>{n&&qo(n,o)&&Oe(n,r,Wo(o,n))})}}),t.reduce((e,n)=>{for(let o=0;o<n.removedNodes.length;o++){let r=n.removedNodes[o];r.nodeType===1&&!document.contains(r)&&e.push(r)}return e},[]).forEach(function e(n){if(n.children&&n.children.length>0)for(let r=0;r<n.children.length;r++)e(n.children[r]);let o=Le.get(n);if(o)for(let r of o.values())r.call(window,n),o.delete(r),It.delete(n)})}),Fo.observe(window.document.body,{childList:!0,subtree:!0}),Do=!0});var Z={};Ze(Z,{container:()=>k,initState:()=>Aa,makeDetectStateChange:()=>Go,makeDispatch:()=>wa,makeInspect:()=>va,subscribe:()=>xa});var k={providers:new Map,subscribers:new Map,relationships:new Map,providerIndex:new WeakMap,subscriberIndex:new WeakMap},Vo=(()=>{let t=0;return()=>t++})();function Go(t){let e={};return n=>{let o=t.filter(r=>e[r]!==n[r]);return t.forEach(r=>{e[r]=n[r]}),o}}var Uo=(t,e)=>{var n,o;return(o=(n=k.subscriberIndex.get(t))==null?void 0:n.get(e))==null?void 0:o.id},Yo=t=>k.providers.get(k.relationships.get(t)),jo=(t,e)=>Yo(Uo(t,e)),ma=(t,e)=>k.providerIndex.has(t)&&k.providerIndex.get(t).has(e),Qo=(t,e)=>{let n=sn(o=>ma(o,e))(t);return n?k.providerIndex.get(n).get(e):null},Pt=new WeakMap;window.addEventListener("rvt-store-provider",()=>{Pt=new WeakMap});var Jo=(t,e)=>(Pt.get(t)||Pt.set(t,{}),Pt.get(t).name||(Pt.get(t).name=k.providers.get(Qo(t,e))),Pt.get(t).name),Zo=(t,e=!1)=>{let n=Yo(t);if(!n)return;let o=k.subscribers.get(t);if(!!o)for(let r of o.values()){let[i,s]=r;i(n.state,s(n.state),e)}},ga=(t,e,n)=>{let o,r=()=>{let s=k.relationships.get(t),a=Qo(e,n);s!==a&&(k.relationships.set(t,a),clearTimeout(o),o=setTimeout(()=>Zo(t,!0),10))},i=tt(window,"rvt-store-provider",r);return r(),()=>{clearTimeout(o),i()}},ya=(t,e)=>[typeof t=="function"?t:()=>{},Go(Array.isArray(e)?e:[])],va=(t,e)=>()=>{var n;return(n=Jo(t,e))==null?void 0:n.state},wa=(t,e)=>n=>{var o;return(o=Jo(t,e))==null?void 0:o.dispatch(n)},xa=(t,e,n=()=>{},o=[])=>{let r=ya(n,o);if(k.subscriberIndex.has(t)||k.subscriberIndex.set(t,new Map),!k.subscriberIndex.get(t).has(e)){let s=Vo();k.subscribers.set(s,new Set),k.subscriberIndex.get(t).set(e,{id:s,teardown:ga(s,t,e)})}return k.subscribers.get(Uo(t,e)).add(r),{unsubscribe:()=>{let{id:s,teardown:a}=k.subscriberIndex.get(t).get(e),c=k.subscribers.get(s);c.delete(r),c.size===0&&(k.subscribers.delete(s),k.relationships.delete(s),k.subscriberIndex.get(t).delete(e),a())},getState:()=>{var s,a;return(a=(s=jo(t,e))==null?void 0:s.state)!=null?a:{}},dispatch:s=>{var a;return(a=jo(t,e))==null?void 0:a.dispatch(s)}}},ba=t=>typeof t!="function"?e=>e:(...e)=>t(...e),Sa=t=>{let e;return n=>{let{state:o,...r}=k.providers.get(t);k.providers.set(t,{...r,state:r.reducer(n(o))}),cancelAnimationFrame(e),e=requestAnimationFrame(()=>{for(let[i,s]of k.relationships)s===t&&Zo(i)})}},Ta=(t,e,{_reducer:n,...o})=>{if(k.providerIndex.get(t)||k.providerIndex.set(t,new Map),k.providerIndex.get(t).has(e))return;let r=ba(n),i=Vo();return k.providers.set(i,{reducer:r,state:r(o),dispatch:Sa(i)}),k.providerIndex.get(t).set(e,i),window.dispatchEvent(new CustomEvent("rvt-store-provider")),()=>{k.providers.delete(i),k.providerIndex.get(t).delete(e)}},Aa=(t,e={},n=window.document.documentElement)=>{if(!t){console.warn("States must set an ID",t,e,n);return}return Ta(n,t,e)};var Ko=Ut(!1);function Xt(t){return Ko.has(t)}Xt.enable=function(t){Ko.set(t,!0)};var wt=(...t)=>vt("action",...t),et=(...t)=>vt("observer",...t),xt=(...t)=>vt("behavior",...t),te=(...t)=>vt("innate",...t),kn=(...t)=>vt("macro",...t),U=(...t)=>vt("event",...t);function Ea(t,e,n={},o="default"){let r={el:t,handle:{type:e,name:o}};return typeof n=="string"?(r.defaultOption=n,r.options={}):r.options=n,Ee(r)}function ee(t){return j(Ce(t.filter(e=>!!e).map(e=>Ea(...e)).filter(e=>!!e)).map(([,e])=>e()))}var{rivetListener:Ca,rivetDispatch:ka,expandElementValue:Xo,onScanLazy:La}=S;st("behavior");st("innate");st("macro",(t,e,n,o)=>t(e,ee,n,o));st("action",(t,e,n,o)=>{let r=()=>void t(e,Xo(e,n));return Ca(e,o,i=>{n.defer?setTimeout(r,0):r()})});st("event",(t,e,n,o)=>t(()=>ka(e,o),Xo(e,n),e));st("observer",(t,e,n,o="",{scan:r})=>{let[i,s=[]]=(typeof t=="function"?[t]:t)||[],a=i(e,n),[c,u]=Array.isArray(a)?a:[a,()=>{}],{unsubscribe:d,getState:f}=Z.subscribe(e,o,c,s),p=r?La(()=>c(f(),[],!1,!0)):()=>{};return[d,u,p]});var{expandElementValue:Oa,getStateValue:Ia}=S;et("outlet",(t,{key:e})=>{let n=t.innerHTML;return o=>{try{let r=Ia(o,Oa(t,e));t.innerHTML=typeof r=="undefined"?n:r}catch(r){console.warn("Unable to update Rivet outlet",r,{key:e,state:o,el:t})}}},{defaultOption:"key"});var{listener:tr}=S,Pa=["click","focus","focusin","focusout","blur"];Pa.forEach(t=>{U(`on${t}`,(e,{preventDefault:n=!0,stopPropagation:o=!1,once:r=!1},i)=>{let s;return tr(i,t,a=>{r&&s||(s=!0,n&&a.preventDefault(),o&&a.stopPropagation(),e())})})});var Ma=["keydown","keyup"];Ma.forEach(t=>{U(`on${t}`,(e,{key:n,preventDefault:o=!0,stopPropagation:r=!1,once:i=!1},s)=>{let a;return tr(document,t,c=>{i&&a||(a=!0,c.key===n&&(o&&c.preventDefault(),r&&c.stopPropagation(),e()))})},{defaultOption:"key"})});var{throttle:Ha,debounce:za,onLoad:Ra,onScanLazy:$a,triggerScan:qa,listener:Ie}=S;U("onready",t=>{setTimeout(()=>void t(),0)});U("onload",t=>Ra(t));U("onexit",(t,{delay:e=1e3,repeat:n=!1})=>{let o,r=!1;return Ie(document,"mouseout",i=>{clearTimeout(o),!i.toElement&&!i.relatedTarget&&!r&&(o=setTimeout(()=>void t(),e),n||(r=!0))})},{defaultOption:"delay"});U("onresize",(t,{throttle:e=50})=>Ie(window,"resize",Ha(t,e,{trailing:!0}),N),{defaultOption:"throttle"});U("onresized",(t,{debounce:e=500})=>Ie(window,"resize",za(t,e,{trailing:!0}),N),{defaultOption:"debounce"});U("onscan",(t,e)=>$a(t,e),{defaultOption:"throttle"});T("img",t=>Ie(t,"load",()=>void qa()));var{animateTopOffset:Da,makeDirectionalEasing:Fa,intersect:Ba}=S,er,nr,Pe=!1,Me=new Map;function or(){er=window.innerHeight}window.addEventListener("resize",or);or();function _a(t,{prop:e,easingFn:n}){let{top:o,height:r}=t.getBoundingClientRect(),i=o+r/2,s=er/2;t.style.setProperty(e,n((i-s)/s))}function rr(){if(!!Pe){for(let[t,e]of Me)_a(t,e);nr=requestAnimationFrame(rr)}}var Wa=(t,e)=>{Me.set(t,e),!Pe&&(Pe=!0,nr=requestAnimationFrame(rr))},ir=t=>{Me.delete(t),Me.size<=0&&(Pe=!1)};xt("intersect",(t,{easing:e="linear",prop:n="--rvt-intersect"})=>[Ba(t,({isIntersecting:r})=>{r?Wa(t,{easingFn:Fa(e),prop:n}):ir(t)},{threshold:0,top:"0px",bottom:"0px"}),()=>void ir(t)],{defaultOption:"prop"});wt("scroll-to-top",(t,{offset:e,speed:n,easing:o="easeInOutExpo"})=>{Da(e,n,o)},{defaultOption:"offset"});kn("scroll-top",(t,e)=>e([[t,"onclick"],[t,"scroll-to-top"]]));var{ensureNumber:sr,updateStateKey:ar,getStateValue:Na,expandElementValue:He}=S;te("define",(t,e={},n)=>{if(!n.match(/^\w+$/)){console.warn("Rivet state keys must be alphanumeric");return}let{_reducer:o,...r}=e.__value||e||{};Z.initState(n,{_reducer:o,...He(t,r)},t)},{defaultOption:"__value",priority:-1});wt("set",(t,{state:e,key:n,value:o})=>{Z.makeDispatch(t,e)(r=>ar(r,He(t,n),o))});var Va=(t,e,n)=>{let o=t.includes(e);return n&&o?t.filter(r=>r!==e):o?t:[...t,e]};wt("list",(t,{state:e,key:n,value:o,toggle:r=!0})=>{Z.makeDispatch(t,e)(s=>{let a=He(t,n),c=Na(s,a);return Array.isArray(c)?ar(s,a,Va(c,o,r)):s})});wt("inc",(t,{state:e,key:n,amount:o=1,min:r=null,max:i=null,wrap:s=!1})=>{let a=Z.makeDispatch(t,e),c=u=>sr(u)+sr(o);a(u=>{let d=He(t,n);return d?{...u||{},[d]:c(u[d])}:c(u)})},{defaultOption:"state"});te("debug",t=>{Xt.enable(t),t.removeAttribute("data-rvt-debug")},{defaultOption:"message"});var{isScalar:cr,getTransitionDuration:Ga,getStateValue:On,expandElementValue:ze,evaluateCondition:Re,listener:Ua,fontCompress:Ya,addClass:lr,removeClass:ja}=S,Qa=(t,e)=>{let n=t||"$v";return cr(n)?cr(e)?`${n}`.replace("$v",e):n==="$v"?"":n:""};et("classname",(t,{key:e,classname:n,condition:o})=>{let r="";return i=>{let s=On(i,ze(t,e)),c=Re(t,o,i,e)?Qa(n,s):"";c!==r&&(r&&t.classList.contains(r)&&t.classList.remove(r),c&&!t.classList.contains(c)&&t.classList.add(c)),r=c}},{defaultOption:"classname"});et("prop",(t,{key:e,prop:n,value:o,condition:r})=>{let i=null;return s=>{let a=On(s,ze(t,e));Re(t,r,s,e)?a!==i&&t.style.setProperty(n,typeof o=="undefined"?a:o):a!==i&&t.style.removeProperty(n),i=a}},{defaultOption:"key"});et("attr",(t,{key:e,attr:n,value:o,condition:r})=>{let i=null;return s=>{let a=On(s,ze(t,e));Re(t,r,s,e)?a!==i&&t.setAttribute(n,typeof o=="undefined"?a:o):a!==i&&t.removeAttribute(n),i=a}},{defaultOption:"key"});et("height",(t,{key:e,condition:n,selector:o})=>{let r,i;return(s,a,c,u)=>{if(e&&!u){let f=ze(t,e);if(s[f]===r)return;r=s[f]}let d=Re(t,n,s,e);setTimeout(()=>{if(d){let[f,...p]=Array.from(t.querySelectorAll(o)).map(l=>l.offsetHeight).sort((l,h)=>h-l);f&&f!==i&&(t.style.setProperty("height",`${f}px`,"important"),i=f)}else t.style.removeProperty("height"),i=null})}},{defaultOption:"selector",scan:!0});window.offscreenTemplates||(window.offscreenTemplates=new WeakMap);xt("offscreen-reset",(t,{mode:e="default"})=>{let n=t.closest("[data-x-toggleable]");if(window.offscreenTemplates.get(t))return;try{let c=document.createElement("textarea");c.innerHTML=t.querySelector('script[type="text/rvt-template"]').textContent;let u=document.createElement("div");u.innerHTML=c.innerText,window.offscreenTemplates.set(t,[c.innerText,u])}catch(c){return console.warn("Unable to locate content template",c),()=>{}}let o,r=()=>{try{let[c,u]=window.offscreenTemplates.get(t);Array.from(u.querySelectorAll("[data-x-toggleable]")).map(f=>f.getAttribute("data-x-toggleable")).forEach(f=>{window.xToggleDelete(f)}),t.innerHTML=c}catch(c){console.warn("Unable to reset offscreen content",c)}},i=()=>{t.innerHTML=""},s=()=>{o=setTimeout(()=>{i(),e==="close"&&r()},Ga(n,300)+100)},a=c=>{clearTimeout(o),c?(e==="open"&&i(),r()):e!=="open"&&s()};return e==="close"&&r(),Ua(n,"tco-toggle",({detail:{state:c}={}})=>void a(c))},{defaultOption:"mode"});xt("font-compress",(t,e)=>Ya(t,e));var Ja=(t,e)=>{try{if(e)return Array.from(t.querySelectorAll(e))}catch{}return t};xt("inner-wrap",(t,{selector:e="",tag:n="span",class:o=""})=>Ja(t,e).map(r=>{let i=document.createElement(n);lr(i,"has-been-tagged"),o&&lr(i,o),Array.from(r.childNodes).forEach(s=>{i.appendChild(s)}),r.append(i),i.offsetHeight,ja(i,"has-been-tagged")}),{defaultOption:"selector"});var In={...Ln};var{toggleClass:ur,listener:nd,PASSIVE_ARGS:Za}=S;T("[data-x-bar]",(t,{scrollButtons:e})=>{if(!e)return;let n=t.querySelector(".x-bar-scroll-inner"),o=t.querySelector(".x-bar-content"),r=t.querySelector('[data-x-bar-scroll-button="bck"]'),i=t.querySelector('[data-x-bar-scroll-button="fwd"]'),s=0,a=0,c=0,u=0,d=()=>{let m=parseInt(window.getComputedStyle(n).width),g=parseInt(window.getComputedStyle(n,":before").width),y=parseInt(window.getComputedStyle(o).width);s=m,a=m-g*2,c=y,u=n.scrollLeft;let v=u<=0,w=c-u-a<=0;ur(r,"is-active",!v),ur(i,"is-active",!w)},f=m=>n.scrollTo({top:0,left:m,behavior:"smooth"}),p=()=>{f(Math.max(u-s,0))},l=()=>{f(Math.min(u+s,c-a))};d();let h=_(d,50);window.addEventListener("resize",h),n.addEventListener("scroll",h,Za),r.addEventListener("mouseup",function(){p()}),i.addEventListener("mouseup",function(){l()})});var at={},Mt={};function Ka(t,e){at[t]||(at[t]=[]),at[t].push(typeof e=="function"?e:()=>e)}function Xa(t,e){if(!at[t])return;let n=at[t].indexOf(e);at[t].splice(n,1)}function tc(t,e){Mt[t]||(Mt[t]=[]),Mt[t].push(e)}function ec(){let t=[...arguments],e=t.shift(),n=t.shift(),o=at[e]?at[e]:[];return(Mt[e]?Mt[e]:[]).forEach(i=>i.call(this,n,...t)),o.reduce((i,s)=>s.call(this,i,...t),n)}var B={filter:Ka,action:tc,apply:ec,filters:at,actions:Mt,remove_filter:Xa};var{animateTopOffset:nc,scrollOffset:oc}=S,Ht,fr=!1,Pn;function dr(){if(!fr){Ht=0;let t=_(dr,50);window.addEventListener("resize",t,N),Pn=document.querySelector("#wpadminbar"),fr=!0}if(Pn){let{position:t,height:e}=window.getComputedStyle(Pn);Ht=t==="fixed"?parseInt(e):0}return Ht}var Y=()=>Ht!=null?Ht:dr(),zt=()=>B.apply("fixed_top_offset",0),rc=(t,e,n)=>{if(t instanceof Element){let{bottom:o,top:r}=oc(t);return e?o-n:r+n}return(typeof t=="number"?t:parseFloat(t))+n},pr=(t,{offsetTop:e=!0,duration:n,easing:o,bottom:r=!1}={})=>{nc(()=>rc(t,r,e?zt():0),n,o)};window.csGlobal=window.csGlobal||{};window.csJsData=window.csJsData||{};window.csGlobal.rivet=In;window.csGlobal._=window.csGlobal.rivet.util;window.csGlobal.everinit=In.attach;window.csGlobal.adminBarOffset=Y;window.csGlobal.fixedTopOffset=zt;window.csGlobal.scrollTo=pr;window.csGlobal.csHooks=B;window.document.documentElement.classList.remove("no-js");window.document.documentElement.classList.add("js");var{onScrollRaw:ic,onResize:sc,oncePassive:hr,elementIndex:mr,addClass:Mn,removeClass:Hn,toggleClass:bd,hasClass:bt}=S,gr=t=>getComputedStyle(t).display!=="none",yr=0,vr=!1,zn=!1;function wr(t){var p,l,h;let e=document.querySelector(".x-site");if(!e){t.style.width="100%";return}var n=window.getComputedStyle(t);if(n.position!=="fixed"){t.style.left="",t.style.width="",t.style.maxWidth="";return}var o=[];xr(n["margin-left"])||o.push(n["margin-left"]),xr(n["margin-right"])||o.push(n["margin-right"]);var r="";o.length>0&&(r=o.length===1?o[0]:"("+o.join(" + ")+")");let i=(h=(l=(p=document.querySelector("body.x-stack-icon .x-sidebar .max.width:not(.x-container)"))==null?void 0:p.parentElement)==null?void 0:l.offsetWidth)!=null?h:0,s=Array.from(document.querySelectorAll(".x-bar-space-v")).reduce((m,g)=>m+g.offsetWidth,i),a=Array.from(document.querySelectorAll(".x-bar-left")).reduce((m,g)=>m+g.offsetWidth,i),c="";a&&(c="calc(0px + "+a+"px)");var u="";s>0&&(u+=" - "+s+"px"),r&&(u+=" - "+r),u+=" - "+yn,t.style.width="calc(100%"+u+")",t.style.left=c;var d=window.getComputedStyle(e),f=d["max-width"];f&&f!=="none"&&(t.style.maxWidth=r?"calc("+f+" - "+r+")":f)}function xr(t){return t.trim().split(" ").filter(e=>!e.match(/^0[a-zA-Z%]+|0$|none$/)).length===0}T("[data-x-bar]",function(t,e){if(e.region==="top"||e.region==="bottom")return S.onViewportChange(()=>{wr(t)},!0)});var $e=/__cs_debug/.test(location.href),br=!1,Rt=[],nt=[],ne=[],Rn,oe=!1;function qe(t){let e=Rt.map(r=>r.id),n=!1,o=Rt.filter(({el:r,id:i})=>{let s=e.indexOf(i);return s!==e.lastIndexOf(i)?(n=!0,e.splice(s,1),!1):window.document.body.contains(r)?!0:(n=!0,!1)});return(n||t)&&(o=o.sort((r,i)=>mr(r.el)-mr(i.el))),n?(Rt=o,nt=Rt,setTimeout($n,0),!0):!1}function $n(){if(qe()||!Y)return;nt=Rt.filter(({el:r})=>gr(r));let t=Y(),e=0,n=0,o=0;uc(),nt.forEach(r=>{var{height:i}=r.el.getBoundingClientRect();r.height=i=Math.round(i),r.hasShrink=Ar(r.props.shrink),r.goal&&r.space&&(r.space.style.height=i+"px"),r.hasShrink&&!r.goal&&(r.shrinkHeight=Tr(i,r.props.shrink)),r.hasShrink||(r.shrinkHeight=r.height),r.triggerOffset=parseInt(r.props.triggerOffset),isNaN(r.triggerOffset)&&(r.triggerOffset=0)}),ne=nt.map((r,i)=>{let{el:s,props:a,height:c,shrinkHeight:u,triggerOffset:d,goal:f}=r,p=o++===0,l=a.zStack&&!p,{marginTop:h}=getComputedStyle(s);var m=parseFloat(h),g=a.keepMargin?m:0,{top:y,bottom:v}=s.getBoundingClientRect();let w=0,C=s.parentNode.getBoundingClientRect().top-Y();C+=window.scrollY;let x=s.parentNode.childNodes;for(let G=0;G<x.length;++G){let rt=x[G];if(!!rt.getBoundingClientRect){if(rt===s)break;bt(rt,"x-bar-space")||bt(rt,"x-bar-is-sticky")||(w+=rt.getBoundingClientRect().height)}}y=C+d+(w-e)+Y();let E=d+w+C+Y(),P=document.body.scrollTop,$=P+y;if(a.triggerSelector)try{let G=document.querySelector(a.triggerSelector);if(G){let{top:rt}=G.getBoundingClientRect();E=rt+P+d-u}}catch(G){$e&&console.warn(G)}else a.hideInitially?(E+=c,E+=d):d>0&&(E+=d+c);l?E+=u:E-=g;let z=Y()+e;qn(E,"red");let L=t+n;a.keepMargin||(L+=m,z-=m),E=Math.floor(E),qn(E,"green"),t=E,l?(z-=u+g,z=Math.max(z,Y())):e+=u+g;let M=a.hideInitially||$<E||$+c<z;n+=c-u;let D=u+e;qn(D,"orange"),a.keepMargin&&(D+=m),D=Math.ceil(D);let pe=g?`calc( -100% - ${g}px)`:"-100%";return f&&z!==ne[i].top&&(s.style.top=`${z}px`),{offset:E,bottom:v,top:z,slide:M,elOffset:$,topOffset:D,translateY:pe,startsFixed:r.startsFixed,firstBar:p,only_show_on_scroll_up:a.only_show_on_scroll_up}}),$e&&nt.forEach(function(r,i){console.log(`Bar: ${i}`,r)}),De()}function De(){if(oe||qe()||zn||document.body.classList.contains("x-body-scroll-disabled"))return;zn=!0;let t=window.scrollY+Y(),e=t-yr,n=e===0?vr:e<0&&window.scrollY!==0;yr=t,vr=n;let o=ne.reduce((i,{offset:s,only_show_on_scroll_up:a,startsFixed:c},u)=>(c?t>=s:t>s)&&(!a||n)?u:i,-1),r=!1;nt.forEach((i,s)=>{let a=i.goal;i.goal=o>=s,a!==i.goal&&(r=!0)}),r&&requestAnimationFrame(Sr),zn=!1}function Sr(){if(oe)return;let t=ac();t?(oe=!0,t(()=>{oe=!1,Sr()})):(oe=!1,window.dispatchEvent(new CustomEvent("cs-sticky-bar-post-transition")),setTimeout(De,0))}function ac(){let t=-1,e=nt.map(n=>{let{goal:o,el:r}=n,i=n.fixedOnce;return{goal:o,fixed:bt(r,"x-bar-fixed")&&i}});if(e.forEach(({goal:n,fixed:o},r)=>{let i=n===o;!i&&t===-1&&(t=r),!i&&r>0&&e[r-1].fixed&&ne[r].slide&&(t=r)}),t!==-1){let n=nt[t],o=n.goal;if(!o&&!bt(n.el,"x-bar-fixed"))return!1;let r=o?cc:lc;return i=>r(nt[t],ne[t],{st:window.scrollY,done:i})}return!1}function cc(t,{top:e,slide:n,elOffset:o,translateY:r,firstBar:i,only_show_on_scroll_up:s},{st:a,done:c}){let{el:u,space:d,content:f,shrinkHeight:p,height:l}=t;t.fixedOnce=!0;let h={top:`${e}px`},m=n||!i&&o+p<a+e||s;if(t.slideEnabled||(m=!1),p&&l!==p){h.height=p;let{paddingTop:w,paddingBottom:b}=getComputedStyle(u);f.style.height=`calc(${p}px - (${w} + ${b}))`,u.style.height=`calc(${p}px - (${w} + ${b}))`}m&&(h.transform=`translate3d( 0, ${r}, 0)`);let g=()=>c();if(Object.entries(h).forEach(([w,b])=>{u.style.setProperty(w,b)}),d){d.style.display="";let w=l+"px";w!==d.style.height&&(d.style.height=w)}Rn.forEach(w=>void Mn(u,w)),Hn(u,"x-bar-is-initially-hidden"),wr(u);let v=window.getComputedStyle(u)["transition-duration"];v=v?parseFloat(v.replace("s","")):0,m?(Mn(u,"x-bar-is-visible"),u.style.transform="",v!==0?hr(u,"transitionend",g):c()):c()}function lc({el:t,space:e,props:n,content:o,shrinkHeight:r,slideEnabled:i},{top:s,slide:a,elOffset:c,translateY:u,firstBar:d,only_show_on_scroll_up:f},{st:p,done:l}){let h=parseFloat(n.shrink),m=!1,g=a||!d&&c+r<p+s||f&&c+r<p+s;i||(g=!1);function y(){let b=window.getComputedStyle(t)["transition-duration"];if(b=b?parseFloat(b.replace("s","")):0,b===0){v();return}hr(t,"transitionend",v),setTimeout(v,b*1e3+100)}n.hideInitially&&Mn(t,"x-bar-is-initially-hidden");function v(){m||(m=!0,o.style.height="",t.style.top="",t.style.transform="",t.style.height="",t.style.left="",t.style.width="",Rn.forEach(w=>Hn(t,w)),Hn(t,"x-bar-is-visible"),e&&(e.style.display="none"),l())}g?(t.style.transform=`translate3d( 0, ${u}, 0)`,y()):!isNaN(h)&&h<1?(o.style.height="",t.style.height="",y()):v()}function uc(){!$e||ke(".cs-sticky-bar-waypoint-debug").forEach(function(t){t.remove()})}function qn(t,e="red",n="white"){if(!$e)return;let o=`<div class="cs-sticky-bar-waypoint-debug" style="position:absolute;height:1px;width:100%;top:${t}px;border-top:1px solid ${e};z-index:999999"><span style="color: ${n};background-color: ${e};left: 0;position: absolute;top: 0; padding: .5em 1em; transform: translate3d(0,-50%,0);";>${t}</span></div>`,r=document.createElement("div");r.innerHTML=o,document.body.appendChild(r.childNodes[0])}function fc(){let t=0;return nt.forEach(function(e){var n;t+=e.goal&&!((n=e.props)==null?void 0:n.zStack)&&e.props.scrollOffset?e.height:0}),t}B.filter("fixed_top_offset",t=>Math.max(t,fc()+Y()));var Dn=()=>{qe(!0)||$n()},Fn=()=>{qe(!0),$n()};function dc(){if(typeof window.ResizeObserver=="undefined")return;let t=window.document.body.clientHeight;new ResizeObserver(_(function(n){!n||document.body.classList.contains("x-body-scroll-disabled")||t!==window.document.body.clientHeight&&(t=window.document.body.clientHeight,Dn())},100)).observe(document.body)}T("[data-x-bar]",(t,e)=>{br||(Rn=(bt(document.body,"x-boxed-layout-active")?"x-bar-fixed x-container max width":"x-bar-fixed").split(" "),V(Dn),sc(Dn),ic(De),dc(),br=!0);let{id:n,region:o}=e;if(!bt(t,"x-bar-is-sticky")||o!=="top")return;var{top:r,height:i}=t.getBoundingClientRect();i=Math.round(i);let s=document.querySelector(`.${n}.x-bar-space`);s&&(s.style.height=i+"px");let a={id:n,el:t,props:e,height:i,topOffset:i,startsFixed:bt(t,"x-bar-fixed"),slideEnabled:!!e.slideEnabled,fixedOnced:!1,shrinkHeight:Tr(i,e.shrink),space:s,scrollOffset:e.scrollOffset,content:t.querySelector(".x-bar-content"),visible:gr(t),startingRectTop:r};return Rt.push(a),Fn(),setTimeout(function(){Fn()},1e3),function(){Fn(),De()}});function Tr(t,e){return Ar(e)?e*t:t}function Ar(t){return!isNaN(t)&&t>0&&parseFloat(t)!==1}var{listener:pc,oncePassive:hc}=S;T('.x-alert [data-dismiss="alert"]',t=>pc(t,"click",e=>{e.preventDefault();let n=t.parentElement;n.classList.remove("in"),n.classList.remove("x-effect-enter"),hc(n,"transitionend",()=>{if(window.csGlobal&&window.csGlobal.isPreview){n.style.display="none";return}n.remove()})}));var{util:mc}=window.csGlobal.rivet,{addClass:re,removeClass:ie,hasClass:Fe}=mc;function $t(t,e){if(e){if(!t||Fe(t,"x-anchor-layered-back"))return;re(t,"x-active")}else ie(t,"x-active")}function Bn(t,e){if(e){if(!t||Fe(t,"x-active")||Fe(t,"x-currently-active"))return;re(t,"x-currently-active")}else ie(t,"x-currently-active")}function Er(t){setTimeout(()=>{let e=t.closest(".x-menu");e.addEventListener("transitionend",function(n){let o=e.getBoundingClientRect().top;if(o<0&&n.propertyName==="height"){let r=0,i=t.closest(".x-bar-v .x-bar-scroll-inner, .x-off-canvas-content, .x-modal")||window;if(i===window){let s=window.pageYOffset,a=parseInt(window.getComputedStyle(document.documentElement).marginTop),c=parseInt(window.getComputedStyle(e).fontSize);r=Math.max(0,o+s-a-c)}i.scrollTo({top:r,left:0,behavior:"smooth"})}})},0)}function gc(t){var e=t.offsetHeight,n=getComputedStyle(t);return e+=parseInt(n.marginTop)+parseInt(n.marginBottom),e}var Cr=t=>Math.max(t.offsetHeight,Array.from(t.children).reduce((e,n)=>e+gc(n)||0,0)),kr=(t,e)=>{let n=t.closest("[data-x-toggle-layered-root]");n&&(n.style.height=`${e}px`)};function yc(t){kr(t,Cr(t));let e=t.closest(`ul:not([data-x-toggleable="${t.getAttribute("data-x-toggleable")}"])`);ie(e,"x-current-layer"),re(e,"x-prev-layer"),re(t,"x-current-layer"),Er(t)}function vc(t){let e=t.closest(`ul:not([data-x-toggleable="${t.getAttribute("data-x-toggleable")}"])`);!Fe(e,"x-prev-layer")||(kr(t,Cr(e)),ie(t,"x-current-layer"),re(e,"x-current-layer"),ie(e,"x-prev-layer"),Er(t))}function Lr(t,e){return e?yc(t):vc(t)}function Or(t,e,n){var o,r,i,s,a=!1,c=!1,u={},d=0,f=0,p={sensitivity:7,interval:100,timeout:0,handleFocus:!1};function l(x,E){return f&&(f=clearTimeout(f)),d=0,c?void 0:n.call(x,E)}function h(x){o=x.clientX,r=x.clientY}function m(x,E){if(f&&(f=clearTimeout(f)),Math.abs(i-o)+Math.abs(s-r)<p.sensitivity)return d=1,c?void 0:e.call(x,E);i=o,s=r,f=setTimeout(function(){m(x,E)},p.interval)}u.options=function(x){var E=x.handleFocus!==p.handleFocus;return p=Object.assign({},p,x),E&&(p.handleFocus?b():C()),u};function g(x){return a=!0,f&&(f=clearTimeout(f)),t.removeEventListener("mousemove",h,!1),d!==1&&(i=x.clientX,s=x.clientY,t.addEventListener("mousemove",h,!1),f=setTimeout(function(){m(t,x)},p.interval)),this}function y(x){return a=!1,f&&(f=clearTimeout(f)),t.removeEventListener("mousemove",h,!1),d===1&&(f=setTimeout(function(){l(t,x)},p.timeout)),this}function v(x){a||(c=!0,e.call(t,x))}function w(x){!a&&c&&(c=!1,n.call(t,x))}function b(){t.addEventListener("focus",v,!1),t.addEventListener("blur",w,!1)}function C(){t.removeEventListener("focus",v,!1),t.removeEventListener("blur",w,!1)}return u.remove=function(){!t||(t.removeEventListener("mouseover",g,!1),t.removeEventListener("mouseout",y,!1),C())},t&&(t.addEventListener("mouseover",g,!1),t.addEventListener("mouseout",y,!1)),u}var{util:wc}=window.csGlobal.rivet,{addClass:_n,transitionEnd:Ir,removeClass:Wn,getOuterHeight:Pr,makeAlternatingSynchronizer:xc}=wc,bc=t=>xc(e=>{if(!t.classList.contains("x-collapsed")){e();return}t.setAttribute("aria-hidden","false");let n=Pr(t);Wn(t,"x-collapsed"),_n(t,"x-collapsing"),t.offsetHeight,t.style.height=`${n}px`,Ir(t,()=>{Wn(t,"x-collapsing"),t.style.height="",e()})},e=>{t.setAttribute("aria-hidden","true"),t.style.height=`${Pr(t)}px`,_n(t,"x-collapsing"),t.offsetHeight,t.style.height="",Ir(t,()=>{Wn(t,"x-collapsing"),_n(t,"x-collapsed"),e()})},void 0,!t.classList.contains("x-collapsed")),Nn=new WeakMap,Sc=t=>(Nn.has(t)||Nn.set(t,bc(t)),Nn.get(t));function Mr(t,e){Sc(t)(e)}var{util:Hr}=window.csGlobal.rivet,{listener:Tc,debounce:zr,addClass:Ac,removeClass:Ec,hasClass:ut,toggleClass:K,oncePassive:Rr,scrollingDisable:Cc,scrollingEnable:$r}=Hr,Be=null,_e=t=>document.querySelector(`[data-x-toggleable="${t}"][data-x-toggle]`),kc=t=>document.querySelector(`[data-x-toggleable="${t}"]:not([data-x-toggle])`),qr=t=>t?t.parentElement.matches(".x-nav-tabs-item")?"classic-tab":t.getAttribute("data-x-toggle"):null,Lc=t=>["tab","classic-tab"].includes(qr(t)),Vn=t=>t&&t.getAttribute("data-x-toggle-group"),Oc=t=>!!document.querySelector(`[data-x-toggle-group="${t||""}"].x-active`),ft=(t,e)=>t&&(t.matches(e)?t:t.closest(e)),Ic=(t,e)=>t&&(t.matches(e)?t:t.querySelector(e)),Pc=t=>ft(t,"[data-x-toggle]"),Dr=t=>ft(t,"[data-x-toggleable]"),Q=t=>t&&t.getAttribute("data-x-toggleable"),We=t=>t&&t.getAttribute("data-x-toggle-hash"),Mc=t=>t&&t.matches(".mce-content-body"),Hc=_(function(){window.dispatchEvent(new Event("resize"))},250),zc=_(function(){window.dispatchEvent(new Event("rvt-scan"))},250);function Rc(t,e=""){return document.querySelector(`[data-x-toggleable=${t}]${e}`)}var $c=t=>{let{marginTop:e,marginBottom:n}=getComputedStyle(t);return t.offsetHeight+parseInt(e)+parseInt(n)},se=()=>window.location.hash.replace("#",""),Fr=t=>document.querySelectorAll(`[data-x-toggle-group="${Vn(t)}"]:not([data-x-toggleable="${Q(t)}"])`),qc=t=>document.querySelectorAll(`[data-x-toggle-group="${Vn(t)}"].x-active`);Hr.toggle={getOthersInGroup:Fr,getActiveInGroup:qc};var Dc=t=>{let e=Pc(t);return[e,Q(e)]},Br=t=>Array.from(document.querySelectorAll("[data-x-toggle-hash]")).filter(e=>We(e)===t).map(Q),ae=t=>ft(t,"[data-x-toggleable]:not([data-x-toggle])"),Fc=30,Ne=(t,e)=>{let n=[],o,r=t,i=0;for(;o=r&&ae(r);){if(i>=Fc){console.warn("Broke toggleable ancestry depth limit ",t,n);break}let s=Q(o);if(r=_e(s),n.includes(s))break;n.push(s),++i}return e&&n.shift(),n};window.TCOToggleStates||(window.TCOToggleStates=new Map,window.TCOToggleScrollBlocking=new Map);var Bc=!1,dt=window.TCOToggleStates,Gn=window.TCOToggleScrollBlocking,pt=!1;function _c(){let t=[];for(let[e,n]of dt){let o=kc(e);n&&Gn.has(e)&&t.push(e),o||dt.delete(e)}t.find(e=>!dt.has(e))&&requestAnimationFrame($r)}window.integrityCheckTimeout||(window.integrityCheckTimeout=null);var Wc=function(){clearTimeout(window.integrityCheckTimeout),window.integrityCheckTimeout=setTimeout(_c,500)};window.xToggleIntegrityCheck||(window.xToggleIntegrityCheck=Wc);function Nc(t){let e=_e(t);switch(qr(e)){case"collapse":case 1:case"layered":return e.matches(".x-active");case"tab":return Oc(Vn(e));case"classic-tab":return e.parentElement.matches(".active");case"collapse-b":return!e.matches(".collapsed")}return e?e.classList&&e.classList.contains("x-active"):null}function qt(t){return dt.has(t)||dt.set(t,Nc(t)),dt.get(t)}function W({id:t,state:e,_triggeringGroup:n,force:o,hashUpdate:r=!window.csGlobal.isPreview}){let i=qt(t);if(typeof e=="undefined"&&(e=!i),e&&window.xLastToggleable!==t)window.xLastToggleable=t,window.xToggleStack.push(t);else if(!e){let a=window.xToggleStack.indexOf(t);a!==-1&&window.xToggleStack.splice(a,1),window.xLastToggleable===t&&(window.xLastToggleable=window.xToggleStack[window.xToggleStack.length-1])}let s=_e(t);!o&&!n&&(Mc(s)||!e&&Lc(s))||(dt.set(t,e),(i!==e||o)&&(Vc(t,e),r&&Zc(s,e)),n||Fr(s).forEach(a=>{W({force:o,id:Q(a),state:!1,_triggeringGroup:!0,hashUpdate:r})}))}window.xLastToggleable="";window.xToggleStack=[];window.xToggleGetState=t=>qt(t);window.xToggleGetStateFromNode=t=>qt(Q(t));window.xToggleUpdate=(t,e)=>W({id:t,state:e});window.xToggleDelete=t=>dt.delete(t);window.xToggleGetId=Q;window.xGetLastToggleable=function(){return window.xLastToggleable};var _r=!1;window.xToggleTempUnlock=()=>{_r=!!pt,pt=!1};window.xToggleTempRelock=()=>{pt=_r};window.xToggleSetLocking=t=>{pt=!!t};window.xToggleHashUpdate=Nr;function Vc(t,e){Array.from(document.querySelectorAll(`[data-x-toggleable="${t}"]`)).forEach(n=>{n.dispatchEvent(new CustomEvent("tco-toggle",{bubbles:!1,detail:{state:e,id:t}}))})}function Wr(t){return Tc(t,"tco-toggle",({currentTarget:e,detail:{state:n,id:o}})=>{var r;if(n||Gc(t),t.hasAttribute("data-x-toggle-overlay")&&(n?((r=document.querySelector(`[data-x-toggleable=${o}][role="dialog"]`))==null?void 0:r.hasAttribute("data-x-disable-body-scroll"))&&(requestAnimationFrame(Cc),Gn.set(o,!0)):n||(Gn.delete(o),requestAnimationFrame($r))),t.hasAttribute("aria-hidden")&&t.setAttribute("aria-hidden",!n),t.hasAttribute("aria-expanded")&&t.setAttribute("aria-expanded",n),t.hasAttribute("aria-selected")&&t.setAttribute("aria-selected",n),t.hasAttribute("data-x-toggle-collapse")?Mr(t,n):t.hasAttribute("data-x-toggle-layered")?Lr(t,n):ut(t,"x-anchor")?$t(t,n):t.getAttribute("data-x-toggle")==="collapse-b"?K(t,"collapsed",!n):ut(t.parentElement,"x-nav-tabs-item")?K(t.parentElement,"active",n):ut(t,"x-tab-pane")?K(t,"active",n):ut(t,"x-dropdown")?Ve(t,n):K(t,"x-active",n),ut(t,"x-modal")&&Hc(),zc(),t.matches("[data-x-toggle]")){if(K(t.querySelector(".x-toggle"),"x-active",n),Bc)return;Array.from(t.querySelectorAll("[data-x-toggle-anim]")).forEach(i=>{ut(i,"x-running")||Rr(i,"animationiteration",()=>{Ec(i,"x-running"),ut(t,"x-active")||i.removeAttribute("style")}),Ac(i,"x-running"),ut(t,"x-active")&&(i.style.animationName=i.getAttribute("data-x-toggle-anim"))})}if(n&&!window.csGlobal.isPreview)if(e.querySelector("[data-x-search][data-x-search-autofocus]")){let i=e.querySelector("[data-x-search][data-x-search-autofocus] input");if(Kc(i,350),!pn())return;Rr(e,"transitionend",function(){i.scrollIntoView(!0)})}else{let i=t.querySelector('[tabindex="-1"]');i&&i.focus&&(i.focus(),setTimeout(function(){i.focus()},250))}})}function Gc(t){let e=t.querySelectorAll("[data-x-toggleable]")||[];for(let n=0;n<e.length;++n){let o=e[n];W({id:o.getAttribute("data-x-toggleable"),state:!1})}}var Uc=t=>{let e=ae(t);return!!(e&&(e.matches(".x-modal")&&!t.closest(".x-modal-content")||e.matches(".x-off-canvas")&&!t.closest(".x-off-canvas-content")))},Yc=(t,{exclude:e=[]}={})=>{if(pt)return;let n=[...Ne(t,Uc(t)),...e].filter(o=>!!o);Array.from(document.querySelectorAll("[data-x-toggleable].x-dropdown, [data-x-toggleable].x-off-canvas, [data-x-toggleable].x-modal")).map(o=>o.getAttribute("data-x-toggleable")).filter(o=>!n.includes(o)).forEach(o=>W({id:o,state:!1}))};function Un(){Nr(se())}function Nr(t){Br(t).forEach(n=>{W({id:n,state:!0})})}var jc=(t,e,n)=>{if(!t||e.isContentEditable)return!1;if(t.matches("[data-x-toggle-hover]")){if(pt)return!0;if(n)return!1}let o=t.querySelector("[data-x-toggle-nested-trigger]");return o?ft(e,"[data-x-toggle-nested-trigger]")===o:ft(e,"[data-x-toggle]")===t};var Yn=({ignoreHoverToggle:t=!0}={})=>e=>{let n=e.target,[o,r]=Dc(n);jc(o,n,t)&&(ft(n,"a[href]")&&e.preventDefault(),W({id:r}));let i=!r&&ft(e.target,"[data-x-toggle-close]"),s=i&&Q(Dr(i));if(s&&W({id:s}),!s&&n.hasAttribute("data-x-toggle-direct-close")){let p=Q(Dr(n));W({id:p})}let a=ft(e.target,"a[href]"),c=a?a.getAttribute("href").replace("#","").trim():"",d=c&&c===se()?Br(se()):[];if(d.length>0&&d.forEach(f=>{W({id:f,state:!0})}),window.xLastToggleable){if(!Rc(window.xLastToggleable,".x-dropdown[data-x-dropdown-direct-close]"))return;Yc(e.target,{exclude:[r,s,...d]})}},R={},Qc=t=>e=>{W({id:t,state:!0})},Jc=t=>e=>(R[t].canHoverLeave=!1,W({id:t,state:!1}),()=>{});function Vr(t){let e=Q(t),n=_e(e);if(!n)return;let o=t.getAttribute("data-x-hoverintent");o=JSON.parse(o||"{}");let{interval:r=100,timeout:i=100}=o;return R[e]={canHoverLeave:!1,cancelEnter:()=>{},cancelLeave:()=>{},hasHoverToggle:()=>!!document.querySelector(`[data-x-toggleable="${e}"][data-x-toggle-hover]`),onEnter:zr(Qc(e),r),onLeave:zr(Jc(e),i)},[q(n,"mouseenter",()=>{R[e].hasHoverToggle()&&(R[e].canHoverLeave=!0,R[e].cancelLeave(),qt(e)||(R[e].cancelEnter=R[e].onEnter()))}),q(n,"mouseleave",()=>{R[e].canHoverLeave&&(pt||(R[e].cancelLeave=R[e].onLeave()),R[e].onEnter.cancel())}),q(t,"mouseenter",()=>{let s=[e,...Ne(ae(t))];setTimeout(()=>{s.forEach(a=>{R[a]&&R[a].cancelLeave()})})}),q(t,"mouseleave",({toElement:s})=>{[e,...Ne(ae(t))].forEach(u=>{R[u]&&R[u].canHoverLeave&&(pt||(R[u].cancelLeave=R[u].onLeave()))}),Ne(ae(s)).forEach(u=>{R[u]&&R[u].cancelLeave()})}),q(n,"touchstart",function(){!R[e].hasHoverToggle()||W({id:e})})]}function Ve(t,e){let o=window.getComputedStyle(t)["transition-duration"];if(o=o?parseFloat(o.replace("s","")):0,Be&&(Be(),Be=null),!o){K(t,"x-active",e),K(t,"x-active-animate",e);return}let r=o*1e3,i=e?"x-active":"x-active-animate",s=e?"x-active-animate":"x-active",a=e?15:r;requestAnimationFrame(function(){K(t,i,e),window.dispatchEvent(new CustomEvent("resize"))});let c=setTimeout(function(){requestAnimationFrame(function(){K(t,s,e)})},a);return Be=function(){!c||(clearTimeout(c),K(t,"x-active",e),K(t,"x-active-animate",e))}}function Gr(t){if(t.tagName==="BUTTON")return;let e=Yn({ignoreHoverToggle:!1});t.addEventListener("keydown",n=>{n.key==="Enter"&&e(n)})}function Ur(t){let e=function(){let n=Ic(t,".x-current-layer"),o=Array.from(n.children).filter(r=>r.matches("li")).reduce((r,i)=>r+$c(i),0);t.style.height=`${o}px`};return e(),lt(e)}function Zc(t,e){let n=We(t);if(!n)return;let o=e?n:"";!e&&`#${n}`!==window.location.hash||`#${o}`!==window.location.hash&&(history.pushState(null,null,"#"+o),window.dispatchEvent(new CustomEvent("hashchange")))}function Kc(t,e){if(e||(e=100),t){var n=document.createElement("input");n.style.position="fixed",n.style.top=t.offsetTop+7+"px",n.style.left=t.offsetLeft+"px",n.style.height=0,n.style.opacity=0,document.body.appendChild(n),n.focus(),setTimeout(function(){t.focus(),t.click(),document.body.removeChild(n)},e)}}var{addClass:jn,siblings:Xc,once:tl,removeClass:Ge,hasClass:ce,listener:le,makeGetComputedStyle:Yr,makeGetComputedFloatValues:el}=S,nl={interval:25,timeout:25,sensitivity:9};function jr(t){sl(t),al()}var Qn=t=>t?t.getBoundingClientRect():null,ol=Yr("position"),rl=Yr("direction"),il=el(["paddingLeft","paddingTop","paddingRight","paddingBottom","borderTopWidth","borderBottomWidth"]);function sl(t){t=Object.assign({selectors:[],indicatingSelector:"a",rootElementEvents:!1,transitionTimeout:null,requireClick(){return!1},toggleOnFocus:!0,activate(r){t.indicatingSelector?jn(r.querySelector(t.indicatingSelector),t.activeClass):jn(r,t.activeClass);let i=r.querySelector(t.nestedSelector);if(ce(i,"x-dropdown")){t.transitionTimeout&&t.transitionTimeout(),t.transitionTimeout=Ve(i,!0);return}jn(i,t.activeClass)},deactivate(r){t.indicatingSelector?Ge(r.querySelector(t.indicatingSelector),t.activeClass):Ge(r,t.activeClass);let i=r.querySelector(t.nestedSelector);if(ce(i,"x-dropdown")){t.transitionTimeout&&t.transitionTimeout(),t.transitionTimeout=Ve(i,!1);return}Ge(i,t.activeClass)},isActive(r){return t.indicatingSelector?ce(r.querySelector(t.indicatingSelector),t.activeClass):ce(r,t.activeClass)},deactivateChildren(r,i){Array.from(r.querySelectorAll(t.nestedSelector)).forEach(s=>{!ce(s,t.activeClass)||(Ge(s,t.activeClass),typeof i=="function"&&i(s))})},deactivateChild:null,activeClass:"x-active",nestedSelector:".sub-menu",findSiblings:null,closeSiblings:!0},typeof t=="object"?t:{});function e(r){var i=!1;r._stemAllowFocusIn=!0;let s=r.closest("[data-x-hoverintent]"),a=s&&s.getAttribute("data-x-hoverintent"),c=a?JSON.parse(a):nl,u=t.rootElementEvents?r:r.querySelector(t.indicatingSelector)||r,d=le(u,"mousedown",m),f=le(u,"touch",m),p=[d,f];if(p.push(le(u,"touchstart",()=>{r._stemAllowFocusIn=!1})),t.toggleOnFocus&&(p.push(le(u,"focusin",l)),p.push(le(r,"focusout",h))),!t.requireClick(r)){p.push(Et(u,"touchstart",()=>{i=!0}));let g=Or(r,function(){i||(d(),f(),n(r,!0))},function(){i||n(r,!1)});try{g.options(c)}catch{}p.push(()=>g.remove())}function l(){r._stemAllowFocusIn&&o(r,n(r,!0))}function h(){setTimeout(()=>{r.contains(document.activeElement)||o(r,n(r,!1))},0)}function m(g){g.type==="mousedown"&&tl(g.currentTarget,"click",v=>void v.preventDefault()),g.preventDefault(),g.stopPropagation();let y=n(r);o(r,y),y&&t.closeSiblings&&(typeof t.findSiblings=="function"?t.findSiblings(r):Xc(r)).forEach(w=>{n(w,!1),o(w,!1)})}return p}t.selectors.forEach(r=>{T(r,e)});function n(r,i){return r._stemAllowFocusIn=!0,typeof i=="undefined"&&(i=!t.isActive(r)),i?(typeof t.beforeActivate=="function"&&t.beforeActivate(r),t.activate(r),typeof t.afterActivate=="function"&&t.afterActivate(r)):(typeof t.beforeDeactivate=="function"&&t.beforeDeactivate(r),t.deactivate(r),typeof t.afterDeactivate=="function"&&t.afterDeactivate(r)),t.isActive(r)}function o(r,i){typeof t.deactivateChildren=="function"&&(clearTimeout(r._stemCloseChildrenTimer),i||(r._stemCloseChildrenTimer=setTimeout(function(){t.deactivateChildren(r,t.deactivateChild)},1e3)))}}function al(){function t(l){var h=[];function m(y,v){if(!v&&y.hasAttribute("data-x-stem")){h.push(y),t(y);return}if(y.children)for(var w=0;w<y.children.length;w++)m(y.children[w])}m(l,!0);let g=function(){r(l),setTimeout(()=>{h.forEach(n)},0)};l.addEventListener("x-stem:update",g,!1)}let e=[];function n(l){l&&l.dispatchEvent(new CustomEvent("x-stem:update"))}T("[data-x-stem-menu-top], [data-x-stem-root]",l=>{requestAnimationFrame(()=>{t(l),n(l),e.push(l)})});let o=_(function(){e.forEach(n)},50);window.addEventListener("tco-toggle",({detail:{state:l}={}})=>{l&&o()},N),window.addEventListener("resize",o,N),window.addEventListener("scroll",o,N),window.addEventListener("cs-sticky-bar-post-transition",o,N),V(o);function r(l){if(!l)return;let h=p(l);if(!h)return;let m=Qn(h),g=a(l),y=g==="data-x-stem-root"?Qn(document.querySelector(`[data-x-toggleable="${l.getAttribute("data-x-toggleable")}"][data-x-toggle]`)):m;if(!y)return;let v=l.getAttribute("data-x-stem-force");if(v){let M=v.indexOf("d")!==-1,D=v.indexOf("r")!==-1;l.setAttribute("data-x-stem",v),g==="data-x-stem-root"&&i(l,m,y,D,M),g||d(l,D,M);return}let w=Qn(l),{top:b,left:C,bottom:x,right:E}=s(w,y,g),{x:P,y:$}=c(l,g),z=u(P,window.innerWidth-E,C),L=u($,window.innerHeight-x,b);l.setAttribute("data-x-stem",(L?"d":"u")+(z?"r":"l")),g==="data-x-stem-root"&&i(l,m,y,z,L),g||d(l,z,L)}function i(l,h,{top:m,left:g,bottom:y,right:v,height:w,width:b},C,x){let E=l.getAttribute("data-x-stem-root")||l.getAttribute("data-x-stem-force")||"",P=E.indexOf("h")!==-1,$=E.indexOf("c")!==-1;if(C&&!$){let L=g-h.left;l.style.left=`${P?L+b:L}px`,l.style.right="auto"}else if($){let L=l.getBoundingClientRect(),M=g-L.width/2+b/2;M=Math.max(0,M);let D=window.innerWidth-L.width;M=Math.min(M,D),l.style.left=`${M}px`,l.style.right="auto"}else{let L=h.right-v;l.style.right=`${P?L+b:L}px`,l.style.left="auto"}let z=window.getComputedStyle(l);if(x){let L=m-h.top,M=P?L:L+w;z.position==="fixed"&&(M+=h.top),l.style.top=`${M}px`,l.style.bottom="auto"}else{let L=h.bottom-y,M=P?L:L+w;z.position==="fixed"&&(M+=h.bottom),l.style.bottom=`${M}px`,l.style.top="auto"}}function s({height:l,width:h},m,g){let y={top:m.top-l,right:m.left+m.width+h,bottom:m.top+l,left:m.left-h};return g&&(y.right+=m.width,y.bottom+=m.height),y}function a(l){return l.hasAttribute("data-x-stem-menu-top")?"data-x-stem-menu-top":l.hasAttribute("data-x-stem-root")?"data-x-stem-root":null}function c(l,h){if(h){var m=rl(l)==="ltr",g=l.getAttribute(h).indexOf("r")!==-1;return{y:!0,x:!!(m^g)}}let y=f(l);return{y:y.indexOf("d")!==-1,x:y.indexOf("r")!==-1}}function u(l,h,m){if(l&&h<0){if(h<m)return!1}else if(m<0&&m<h)return!0;return l}function d(l,h,m){let{paddingLeft:g,paddingTop:y,paddingRight:v,paddingBottom:w,borderTopWidth:b,borderBottomWidth:C}=il(l);if(h?(l.style.marginLeft=g!==0?`${g}px`:null,l.style.marginRight=null):(l.style.marginRight=v!==0?`${v}px`:null,l.style.marginLeft=null),m){let x=y+b;l.style.marginTop=x!==0?`${x*-1}px`:null,l.style.marginBottom=null}else{let x=w+C;l.style.marginBottom=x!==0?`${(w+C)*-1}px`:null,l.style.marginTop=null}}function f(l){return l.parentElement===null?"tr":l.parentElement.hasAttribute("data-x-stem-force")?l.parentElement.getAttribute("data-x-stem-force"):l.parentElement.hasAttribute("data-x-stem")?l.parentElement.getAttribute("data-x-stem"):f(l.parentElement)}function p(l){if(l.parentElement===null)return document.body;let h=ol(l.parentElement);return h==="relative"||h==="absolute"?l.parentElement:p(l.parentElement)}}var{onLoad:Qr,addClass:Gd,hasClass:Jr,removeClass:Ud,debounce:Zr,animateToElement:cl,listener:Kr,listenerPassive:Xr,onScroll:ll,onResize:ul,PASSIVE_ARGS:fl}=S,ti=t=>t.match(/#[a-zA-Z]/);jr({selectors:[".x-menu-inline .menu-item-has-children",".x-menu-dropdown .menu-item-has-children"],beforeActivate:t=>$t(t.querySelector("a"),!0),beforeDeactivate:t=>$t(t.querySelector("a"),!1),deactivateChild:t=>$t(t.querySelector("a"),!1)});Qr(()=>{let{selector:t,duration:e,easing:n,initialMove:o,before:r,after:i,allowScroll:s}=B.apply("hash_scrolling_config",{selector:window.csJsData.linkSelector,easing:"ease-out",duration:500,initialMove:!0,before:f=>B.apply("hash_scrolling_before",f),after:f=>B.apply("hash_scrolling_before",f),allowScroll:(...f)=>B.apply("hash_scrolling_allow",!0,...f)});function a(f){try{return document.querySelector(f)}catch{}return null}function c(){let f=!1;return Xr(document.body,"touchstart",()=>{f=!1}),Xr(document.body,"touchmove",()=>{f=!0}),()=>f}let u=(f,p=!0,l)=>{cl(f,()=>zt()*-1,p?e:0,n,l)},d=c();o&&window.location.hash&&(u(a(window.location.hash),!1),setTimeout(()=>u(a(window.location.hash),!1),300)),Kr(document.body,"click",f=>{let p=f.target.matches(t)?f.target:f.target.closest(t);if(!p||f.tcoAbortScroll)return;let l=p.getAttribute("href"),h=l.split("#");if(!h[1])return;let m=h[0].replace(location.origin,""),g=`#${h[1]}`;if(d()||m&&m!==location.pathname||!ti(l)||!s(p,f,g))return;let y=a(g);!y||(window.history.pushState&&(window.history.pushState(null,null,g),window.dispatchEvent(new CustomEvent("tcoHistoryPush"))),f.preventDefault(),r({anchor:p,target:y,hash:g}),u(y,!0,()=>void i({anchor:p,target:y,hash:g})))})});Qr(()=>{let t=!1,e,n=new Map,o=new Map,r=[],i=Zr(()=>{var f,p;let d=[];for(let[l,h]of o){let m=((p=(f=h.getBoundingClientRect())==null?void 0:f.top)!=null?p:0)+window.scrollY;d.push({y:m,href:l})}r=d.sort((l,h)=>h.y-l.y)},100),s=d=>{if(e!==d){for(let[f,p]of n)if(f===d)for(let l of p)Jr(l,"x-anchor")&&Bn(l,!0),B.apply("scrollspy_activate",l);else for(let l of p)Jr(l,"x-anchor")&&Bn(l,!1),B.apply("scrollspy_deactivate",l);e=d}};T(window.csJsData.linkSelector,d=>{let f=B.apply("scrollspy_ignore_patterns",["#/","#wp-toolbar"]),p=`#${d.getAttribute("href").trim().split("#").pop()}`;if(!(!ti(p)||f.find(l=>l.match(p))))return n.has(p)||n.set(p,new Set),n.get(p).add(d),[T(p,l=>(o.has(p)||o.set(p,l),i(),()=>{o.has(p)&&o.delete(p)})),()=>{var l;(l=n.get(p))==null||l.delete(d)},Kr(d,"click",()=>{t=!0,s(p)},{capture:!0})]}),ul(i);let a=()=>{var f,p;let d=window.scrollY+zt()+Y()+1;return Math.ceil(window.innerHeight+window.scrollY)>=Math.floor(document.body.offsetHeight)?(p=(f=r.map((l,h)=>({...l,i:h})).sort((l,h)=>h.y-d-(l.y-d))[0])==null?void 0:f.i)!=null?p:-1:r.findIndex(({y:l})=>d>=l)},c=()=>{t=!1;let d=a();s(d===-1?"":r[d].href)},u=Zr(c,250);u(),window.addEventListener("hashchange",function(d){s(window.location.hash)},fl),ll(()=>{r.length<=0||(t?u():c())})});var{transitionEnd:dl,addClass:Jn,removeClass:Zn,farthest:ei,getCachedJsonAttribute:ni,oncePassive:jd,listenerPassive:pl,evaluateCondition:hl,expandElementValue:oi,makeAlternatingSynchronizer:Kn,makeElementWeakMap:Xn,getDurations:ml,lockMotion:ri,forceOpaque:gl,runAnimation:ii,elementMeta:yl,waypoint:si,parseTime:vl}=S,St="x-effect-enter",to="x-effect-entering",ot="x-effect-exit",eo="x-effect-exiting",Ue="x-effect-animated",Ye="x-effect-holding",wl="x-effect-opacity";T("[data-x-single-anim]",(t,e)=>{if(!t.classList.contains("x-always-active"))return pl(t.closest(".x-anchor, .x-text"),"mouseenter",()=>void ii(t,{animation:e,remove:!0}))});var ai=Xn({scrollEffects:!1}),xl=()=>{let{get:t,set:e,has:n}=Xn();return o=>(n(o)||e(o,{effects:[],particles:[]}),t(o))},je=xl(),ci=Xn(0),li=t=>{ci.set(t,ml(t))},ui=(t,e="transitionTime")=>{var n,o;return(o=(n=ci.get(t))==null?void 0:n[e])!=null?o:0},fi=(t,{from:e,to:n,trans:o,record:r=!1},i=()=>{})=>(t.classList.remove(e),t.classList.add(o),t.classList.add(n),r&&li(t),t.csAnimationEndingTimeout&&clearTimeout(t.csAnimationEndingTimeout),()=>{t.csAnimationEndingTimeout=setTimeout(function(){t.classList.remove(o)},250),i()}),di=(t,e,n=()=>{})=>ii(t,{className:Ue,animation:e,remove:!0,timeout:!0},n),bl=(t,e)=>{t.classList.contains(ot)||(t.classList.contains(Ue)||t.classList.add(Ue),t.style.setProperty("animation-duration","0ms","important"),t.style.setProperty("animation-name",e))},pi=(t,e,n)=>dl(t,fi(t,e,n)),hi=(t,e,n,o)=>di(t,e,fi(t,n,o)),Sl=(t,e,n)=>hi(t,e,{from:ot,to:St,trans:to,record:!0},n),mi=(t,e,n)=>hi(t,e,{from:St,to:ot,trans:eo},n),Tl=(t,e)=>pi(t,{from:ot,to:St,trans:to,record:!0},e),gi=(t,e)=>pi(t,{from:St,to:ot,trans:eo},e),Al=(t,e,n)=>{Jn(t,Ye),setTimeout(gl(t,{after:()=>{Zn(t,Ye),n()}}),e)},yi=(t,e,n)=>{let o=ri(t,e==="fade"?"opacity":null);t.style.setProperty("opacity",e==="fade"?0:1,"important");let r=ui(t);Jn(t,Ye),setTimeout(()=>{Zn(t,St),Zn(t,Ye),Jn(t,ot),t.style.removeProperty("opacity"),o(),n()},r)};function El(t,e,n){let o,r=0,i,s,{durationBase:a,animationAlt:c}=ni(t,"data-x-effect"),u=vl(a),d=()=>{window.removeEventListener("mousemove",g),c?n(()=>{f()}):(n(),u?setTimeout(()=>{f()},u):f())},f=()=>{t.addEventListener("mouseenter",h)},p=()=>{let{top:v,left:w,width:b,height:C}=t.getBoundingClientRect();o.push({top:v+window.scrollY,left:w+window.scrollX,width:b,height:C})},l=v=>{r=0,o=[],p(),e(),u?(clearTimeout(i),i=setTimeout(()=>{p()},u)):p()},h=v=>{clearTimeout(s),window.addEventListener("mousemove",g),l(v)},m=v=>{r++,r>10&&o.length===o.filter(b=>y(b,v.clientX,v.clientY,window.scrollX,window.scrollY)).length&&d()},g=v=>{m(v),clearTimeout(s),s=setTimeout(()=>{m(v)},300)},y=({top:v,left:w,height:b,width:C},x,E,P,$)=>{let z=v-$,L=w-P,M=x>L&&x<L+C,D=E>z&&E<z+b;return!M||!D};f()}T("[data-x-effect-provider]",(t,e="")=>{e.split(" ").filter(o=>!ei(t,`[data-x-effect-provider*="${o}"]`)).length>0&&no(t)});function no(t){let e=je(t);e.registered||El(t,()=>{let{registered:n,...o}=je(t);Object.keys(o).forEach(r=>{o[r].forEach(({setup:i})=>{i&&i()})})},()=>{let{registered:n,...o}=je(t);Object.keys(o).forEach(r=>{o[r].forEach(({teardown:i})=>{i&&i()})})}),e.registered=!0}function vi(t,e,n=()=>{},o=()=>{},r=!0){let i=ei(e,`[data-x-effect-provider*="${t}"]`),s=r?i||e:i;return s?(je(s)[t].push({el:e,setup:n,teardown:o}),e===s):!1}T(".x-anchor",t=>{vi("particles",t)&&no(t)});T("[data-x-effect]",(t,e)=>{try{return vi("effects",t,()=>{e.animationAlt&&di(t,e.animationAlt)},()=>{},!t.matches("x-anchor"))&&no(t),Hl(t,e)}catch(n){console.warn(n)}},1e3);var Cl=(t,e)=>Kn(n=>Tl(t,n),n=>e==="transform"?gi(t,n):yi(t,e,n)),kl=(t,e,{animationEnter:n,animationExit:o})=>Kn(r=>Sl(t,n,r),r=>e==="animation"?mi(t,o,r):yi(t,e,r)),Ll=(t,e,n)=>Kn(o=>{if(li(t),["transform","animation"].includes(e)){e==="animation"&&(t.classList.remove(Ue),t.style.setProperty("opacity",0,"important"),t.style.removeProperty("animation-name"),t.offsetHeight,t.style.removeProperty("opacity"));let r=ri(t,"opacity");t.classList.remove(ot),r()}o()},o=>{switch(e){case"none":return Al(t,ui(t),o);case"transform":return gi(t,o);case"animation":return mi(t,n.animationExit,o);default:o()}}),wi=(t,e,n)=>{let o=ni(t,"data-x-effect");if(o.scroll){let r=a=>a==="effect"?o.animationEnter&&o.animationExit?"animation":"transform":a;e!=="effect"&&n!=="effect"&&(t.classList.add("x-no-at"),t.classList.remove("x-effect-exit"),setTimeout(()=>{t.classList.remove("x-no-at")}));let i=r(e),s=r(n);switch(s==="animation"&&bl(t,o.animationExit),e==="effect"&&["fade","none"].includes(n)&&t.classList.add(wl),i){case"animation":return kl(t,r(n),o);case"transform":return Cl(t,s,o);case"fade":return Ll(t,s,o)}}return!1};et("effects",(t,{key:e,condition:n,enter:o,exit:r})=>{ai.set(t,{scrollEffects:"managed"});let i=wi(t,oi(t,o),oi(t,r));return i?s=>void i(hl(t,n,s,e)):()=>{}});var Ol=(t,{behaviorScroll:e})=>{let n=wi(t,"effect","effect"),o=!1;return r=>{let[i,s]=r.split(":");e==="reset"&&i==="exit"&&s==="down"||e==="fire-once"&&o||(i==="enter"&&(o=!0),n(i==="enter"))}},Il=t=>{let e=parseInt(t);return t.includes("px")?()=>e:()=>window.innerHeight*e/100},Pl=t=>{let e=parseInt(t);return t.includes("px")?()=>window.innerHeight-e:()=>window.innerHeight-window.innerHeight*(parseInt(e)/100)};function Ml(t,e){let n,o=()=>{},r=()=>{let{effectRivet:i}=yl.get(e);i?o=ee([[t,...i]]):setTimeout(r,10)};return r(),()=>{clearTimeout(n),o()}}function Hl(t,e){let n=t.closest("[data-x-slide], [data-x-slide-goto]");if(n&&t!==n)return Ml(t,n);if(!e.scroll||ai.get(t).scrollEffects==="managed")return;if(window.csGlobal.isPreview&&t.classList.add(ot),e.forceScrollEffect){t.classList.remove(St),t.classList.remove(to),t.classList.remove(ot),t.classList.remove(eo),e.forceScrollEffect==="in"&&t.classList.add(St),e.forceScrollEffect==="out"&&t.classList.add(ot);return}let o=Ol(t,e),{offsetTop:r="10%",offsetBottom:i="10%"}=e;si(t,s=>void o(`${s==="up"?"enter":"exit"}:${s}`),Il(r),!1),si(t,s=>void o(`${s!=="up"?"enter":"exit"}:${s}`),Pl(i),!1)}var{defer:zl,addClass:xi,toggleClass:Rl,removeClass:$l,listenerPassive:Dt}=S;T("[data-x-search]",function(t){let e=t.querySelector("input"),n=()=>zl(()=>e.focus()),o=()=>Rl(t,"x-search-has-content",!!e.value);return[Dt(e,"input",o),Dt(t,"mousedown",()=>{xi(t,"x-search-focused"),n()}),Dt(t,"focusin",()=>void xi(t,"x-search-focused")),Dt(t,"focusout",()=>void $l(t,"x-search-focused")),Dt(t.querySelector("[data-x-search-clear]"),"click",()=>{e.value="",e.focus(),o()}),Dt(t.querySelector("[data-x-search-submit]"),"click",()=>{window.csGlobal.isPreview||t.submit()})]});T("[data-x-element-bg-layer]",(t,{parallaxDir:e="v",parallaxRev:n=!1,parallaxSize:o=""})=>{let r=t.closest(".x-bg"),i=e==="h";t.style.opacity="1",i?(n||(t.style.left="auto",t.style.right="0"),t.style.width=o):(n||(t.style.top="auto",t.style.bottom="0"),t.style.height=o);let s=!1;function a(){if(s)return;s=!0;let{width:u,height:d,top:f,bottom:p}=r.getBoundingClientRect();if(f<=window.innerHeight&&p>=0){let{width:l,height:h}=t.getBoundingClientRect(),m=n?-1:1,g=i?l-u:h-d,y=1-p/(window.innerHeight+d),v=`${parseInt(y*g*m,10)}px`,w=i?`translate3d(${v}, 0, 0)`:`translate3d(0, ${v}, 0)`;t.style.transform=w}s=!1}function c(){requestAnimationFrame(a)}return[un(c),lt(c),V(c)]});V(function(){T("[data-x-scroll-link]",function(t,e){let n=document.querySelector(e);return q(t,"wheel",function(o){n.scrollTop+=o.deltaY})})});var{addClass:ql,removeClass:bi,hasClass:Dl,listenerPassive:ue}=S;function oo(t,e){(e!=null?e:!Dl(t,"is-active"))?(bi(t,"has-not-flipped"),ql(t,"is-active")):bi(t,"is-active")}var Qe=!1;T("[data-x-element-card]",t=>[ue(t,"touchstart",()=>{Qe=!0}),ue(t,"click",({target:e})=>{t.contains(e.closest("a"))||oo(t)}),ue(t,"pointerenter",()=>{setTimeout(function(){Qe||oo(t,!0)},15)}),ue(t,"pointerleave",()=>{Qe||oo(t,!1)}),ue(t,"touchend",()=>{setTimeout(function(){Qe=!1},100)})]);var{makeRafLoop:Fl}=S;function Bl(t,e){let n=`${t}`;for(;n.length<e;)n=`0${n}`;return n}function _l(){let t=new WeakMap;return function(e,n){t.has(e)||t.set(e,e.innerHTML),n!==t.get(e)&&(e.innerHTML=n,t.set(e,n))}}function Si({el:t,end:e,serverTime:n,leadingZeros:o=!0,hideEmpty:r=!0,loadingClass:i="is-loading",completeClass:s="is-complete",digitClass:a="x-countdown-digit",completeMessageTag:c="div",completeMessageContent:u="",completeMessageClass:d="x-countdown-complete",hideOnComplete:f=!1,selectors:p={days:"[data-x-countdown-d]",hours:"[data-x-countdown-h]",minutes:"[data-x-countdown-m]",seconds:"[data-x-countdown-s]",daysLabel:"[data-x-countdown-label-d]",hoursLabel:"[data-x-countdown-label-h]",minutesLabel:"[data-x-countdown-label-m]",secondsLabel:"[data-x-countdown-label-s]",parent:"[data-x-countdown-unit]",aria:"[data-x-countdown-aria]"},singularLabels:l={d:"Day",h:"Hour",m:"Minute",s:"Second"},pluralLabels:h={d:"Days",h:"Hours",m:"Minutes",s:"Seconds"},ariaLabel:m="Countdown ends in {{d}} days, {{h}} hours, and {{m}} minutes."}={}){if(!t)return;let g={days:t.querySelector(p.days),hours:t.querySelector(p.hours),minutes:t.querySelector(p.minutes),seconds:t.querySelector(p.seconds)},{days:y,hours:v,minutes:w,seconds:b}=g,C={d:t.querySelector(p.daysLabel),h:t.querySelector(p.hoursLabel),m:t.querySelector(p.minutesLabel),s:t.querySelector(p.secondsLabel)},x=new Date(e).getTime(),E=new Date(n).getTime()-new Date().getTime(),P=_l(),$=I=>{let H=Math.abs(I),F=parseInt(H/86400);H%=86400;let it=parseInt(H/3600);H%=3600;let Tt=parseInt(H/60);H%=60;let po=parseInt(H);return y||(it+=F*24),v||(Tt+=it*60),w||(po+=Tt*60),{diffDays:F,diffHours:it,diffMinutes:Tt,diffSeconds:po}},z=()=>{if(Object.keys(g).forEach(G),f){t.style.display="none",P(t,"");return}if(!u)return;let I=document.createElement(c);I.innerHTML=u,d&&I.classList.add(d),t.append(I),t.classList.add(s)},L=I=>(o?Bl(I,2):I.toString()).split("").map(F=>`<span class="${a}">${F}</span>`).join(""),M=(I,H)=>{if(!I||!C[I])return;let F=C[I];P(F,(H?l:h)[I])},D=({diffDays:I,diffHours:H,diffMinutes:F,diffSeconds:it})=>{y&&(P(y,L(I)),M("d",I===1)),v&&(P(v,L(H)),M("h",H===1)),w&&(P(w,L(F)),M("m",F===1)),b&&(P(b,L(it)),M("s",it===1))},pe=({diffDays:I,diffHours:H,diffMinutes:F,diffSeconds:it})=>{let Tt=t.querySelector(p.aria);Tt&&P(Tt,m.replace(/{{d}}/g,I).replace(/{{h}}/g,H).replace(/{{m}}/g,F).replace(/{{s}}/g,it))},G=I=>{if(!g[I])return;let H=g[I].closest(p.parent);H&&(H.remove(),g[I]=null)},rt=({diffDays:I,diffHours:H,diffMinutes:F})=>{I===0&&G("days"),I===0&&H===0&&G("hours"),I===0&&H===0&&F===0&&G("minutes")},Hi=E+new Date().getTime();return Fl(I=>{let H=Hi+I;if(H>x)return z(),!1;let F=$((H-x)/1e3);r&&rt(F),D(F),pe(F),I===0&&t.classList.remove(i)})}T("[data-x-element-countdown]",(t,e={})=>Si({el:t,...e}));var{waypoint:Wl,tween:Nl,getPrecisionLength:Vl,getPrecisionLengthWithCommas:Gl,round:Ul}=S,Yl=",",Ti=".",jl=/(-)?(\d+)(\.\d+)?/;function Ql({el:t,from:e,to:n,commaSeparatedDecimal:o=!1,...r}){let i=typeof e=="undefined"?t.textContent:e,s=typeof n=="undefined"?t.textContent:n,a=o?Ti:Yl,c=s.toString().includes(a)||i.toString().includes(a),u=o?Gl:Vl,d=Math.max(u(s),u(i)),f=h=>{if(!h)return console.warn("Input invalid",h),"";let[,m="",g="",y=""]=h.match(jl),v=g.split("").reverse(),w=[];for(;v.length;)w.push(v.splice(0,3).reverse().join(""));return`${m}${w.reverse().join(a)}${y}`},p=Ai(i,a),l=Ai(s,a);Nl(p,{...r,update:h=>{let m=Ul(h,Math.pow(10,d)).toFixed(d),g=c?f(m):m;t.textContent=o?g.replace(/\.(\d+)$/,",$1"):g}})(l)}function Ai(t,e){let n=0;return e===Ti&&(n=t.split(",")||[],n=n[1]||0,n&&n.length&&(n=n/Math.pow(10,n.length))),parseFloat(t.replace(new RegExp("\\"+e,"g"),""))+n}T("[data-x-element-counter]",(t,{to:e,speed:n,selector:o=".x-counter-number",commaSeparatedDecimal:r})=>Wl(t,()=>void Ql({el:t.querySelector(o),to:e,commaSeparatedDecimal:r,duration:n}),"85%"));function Ei(t,e={}){try{return JSON.parse(t)}catch{}return e}var{intersect:Jl,hasClass:ct,addClass:Zl,removeClass:ro,unwrapHtmlTemplate:Kl,dispatch:Ci,listener:Je,onResize:Xl,elementIsVisibleInViewport:tu}=S,eu=["playpause","progress"],nu=["playpause","current","progress","duration","tracks","volume","fullscreen"];T("[data-x-element-mejs]",(t,{poster:e,options:n={}})=>{let o=ct(t,"bg")||ct(t,"x-video-bg"),r=ct(t,"vimeo")||ct(t,"youtube"),i=t.getAttribute("data-x-video-options")||n;i=typeof i=="string"?Ei(i):i||{};let s=[];if(o&&(e&&(Zl(t,"poster"),t.style.backgroundImage=`url(${e})`,setTimeout(()=>void ro(t,"transparent"),500)),Kl(t.querySelector('script[type="text/template"]')),s.push(Je(t,"xmejs-start",()=>{ro(t.querySelector(".transparent"),"transparent"),ro(t,"transparent")}))),!window.mejs)return;let a=t.querySelector(".x-mejs");if(!a||ct(a.parentElement,"mejs-mediaelement"))return;let c=l=>{console.warn("MEJS media error.",l),l.stopPropagation()},u=ct(a,"advanced-controls")?nu:eu;u=window.csGlobal.csHooks.apply("cs_mejs_video_features",u,t);let d=window.csGlobal.csHooks.apply("cs_mejs_video_player_args",{pluginPath:window._wpmejsSettings.pluginPath,startVolume:1,features:o?[]:u,audioWidth:"100%",audioHeight:32,audioVolume:"vertical",videoWidth:"100%",videoHeight:"100%",videoVolume:"vertical",pauseOtherPlayers:!1,alwaysShowControls:!0,hideVolumeOnTouchDevices:!1,setDimensions:!1,stretching:"responsive",autoRewind:!1,success:f,error(l){c(l)}});try{window.jQuery(a).mediaelementplayer(d)}catch(l){c(l)}function f(l,h,m){let g=!0,y=!0;ct(t,"autoplay")&&(!i.pause_out_of_view||tu(t))&&(h.setAttribute("autoplay",!0),setTimeout(()=>{h.play()},100));let v=()=>{h.attributes.hasOwnProperty("autoplay")&&g&&(l.play(),g=!1),h.attributes.hasOwnProperty("muted")&&y&&(l.setMuted(!0),y=!1),l.removeEventListener("canplay",v)};l.addEventListener("canplay",v);let w=m.controls[0].querySelector(".mejs-volume-button");l.addEventListener("volumechange",()=>l.setVolume(ct(w,"mejs-mute")?1:0)),l.addEventListener("ended",()=>{h.attributes.hasOwnProperty("loop")&&l.play()}),o||l.addEventListener("playing",()=>{Object.keys(window.mejs.players).filter(b=>b!==m.id&&!window.mejs.players[b].xIsVideoBG).map(b=>window.mejs.players[b]).forEach(b=>b.pause())}),m.isVideo===!0&&(m.xIsVideoBG=o,p(l,h,m))}function p(l,h,{container:m,controls:g}){if(l.addEventListener("timeupdate",function v(){Ci(t,"xmejs-start"),l.removeEventListener("timeupdate",v)}),Je(m[0],"fullscreenchange",()=>{document.fullscreenElement||l.removeAttribute("style")}),r&&s.push(Je(t,"xmejs-start",()=>{var v;(v=t.querySelector("video.x-mejs"))==null||v.removeAttribute("poster")})),o)l.addEventListener("playing",()=>{l.setMuted(!0),Ci(t,"xmejs-bgvideoready")}),s.push(Je(t,"xmejs-bgvideoready",y)),s.push(Xl(y));else{let v=()=>g.stop().animate({opacity:1},150),w=()=>g.stop().animate({opacity:0},150);l.addEventListener("playing",()=>m.on("mouseenter",v).on("mouseleave",w)),l.addEventListener("pause",()=>{m.off("mouseenter mouseleave"),v()})}i.pause_out_of_view&&s.push(ou(t,l));function y(){let v=t.querySelector(r?".me-plugin":"video"),w=h.videoWidth,b=h.videoHeight,C=r||w===0?1280:w,x=r||b===0?720:b,E=t.offsetWidth||0,P=t.offsetHeight||0,$=E/C,z=P/x,L=$>z?$:z,M=Math.ceil(L*C+20),D=Math.ceil(L*x+20),pe=Math.ceil((M-E)/2),G=Math.ceil((D-P)/2);v.style.width=`${M}px`,v.style.height=`${D}px`}}return s});function ou(t,e){let n=ct(t,"autoplay");return Jl(t,function(o){if(o.isIntersecting){n&&e.play();return}e.pause()})}var{waypoint:ru}=S;T("[data-x-element-statbar]",(t,{triggerOffset:e}={})=>{ru(t,()=>{Array.from(t.querySelectorAll(".x-statbar-bar, .x-statbar-label")).forEach(n=>{n.classList.add("x-active")})},e)});var{listener:lp,onLoad:iu,onResize:su,getOuterHeight:au}=S;T("[data-x-element-tabs]",(t,{equalPanelHeight:e})=>{if(!e)return;let n=Array.from(t.querySelectorAll(".x-tabs-panels")),o=()=>{let r=n.reduce((i,s)=>Math.max(i,au(s)),0);n.forEach(i=>{i.style.height=`${r}px`})};return o(),[iu(o),su(o)]});T("script[data-cs-late-style]",function(t){let e=document.createElement("style");e.setAttribute("id",`cs-late-css-${t.getAttribute("data-cs-late-style")}`),e.appendChild(window.document.createTextNode(t.textContent)),window.document.head.appendChild(e),t.remove()});var{listener:ki,PASSIVE_ARGS:cu}=S;T("[data-x-toggleable]",t=>Wr(t));var io=!1,Li=!1;document.addEventListener("readystatechange",()=>{document.readyState==="complete"&&!Li&&(Li=!0,window.document.body.addEventListener("click",Yn()),T("[data-x-toggleable]",t=>{let e=Q(t),n=We(t);(qt(e)||n&&n===se())&&(window.csGlobal.isPreview&&(io=!0,t.setAttribute("data-x-disable-animation",!0)),W({id:e,state:!0,force:!0,hashUpdate:!1}),io&&(io=!1,setTimeout(()=>{t.removeAttribute("data-x-disable-animation")},60)))}))},cu);T("[data-x-toggleable]:not([data-x-toggle])",Vr);T("[data-x-toggle]",Gr);V(function(){let t=[];T("[data-x-esc-close]",function(e){let n=Q(e);return n||console.warn("No toggle id setup for element, but using data-x-esc-close",e),t.push(n),function(){let o=t.indexOf(n);o!==-1&&t.splice(o,1)}}),q(window,"keyup",function(e){if(e.key!=="Escape")return;let n=window.xGetLastToggleable();!n||!window.xToggleGetState(n)||!t.includes(n)||W({id:n,state:!1})})});T("[data-x-toggle-layered-root]",Ur);B.filter("hash_scrolling_allow",(t,e,n)=>n.target.hasAttribute("data-x-toggle-nested-trigger")||n.target.hasAttribute("data-x-skip-scroll")?!1:t);B.action("hash_scrolling_before",({anchor:t})=>{let e=t.closest(".x-modal.x-active, .x-off-canvas.x-active");e&&!(t.hasAttribute("data-x-toggleable")&&!t.querySelector("[data-x-toggle-nested-trigger]"))&&W({id:e.getAttribute("data-x-toggleable"),state:!1})});U("ontoggleclose",(t,e,n)=>ki(n.closest("[data-x-toggleable]"),"tco-toggle",o=>{o.detail.state&&t()}),{defaultOption:"throttle"});U("ontoggleopen",(t,e,n)=>ki(n.closest("[data-x-toggleable]"),"tco-toggle",o=>{o.detail.state||t()}),{defaultOption:"throttle"});window.addEventListener("tcoHistoryPush",Un,!1);window.addEventListener("hashchange",Un,!1);var{onScroll:lu,onResize:Oi,addClass:so,removeClass:ao,toggleClass:uu,onLoad:co,tween:Ii,getOuterHeight:lo,scrollOffset:uo,listener:ht,oncePassive:Pi,getCachedJsonAttribute:fo,waypoint:fe,fontCompress:fu}=S;function J(t,e){T(`[data-x-element="${t}"]`,n=>void e.call(n,n,fo(n,"data-x-params")))}T("[data-x-element-responsive-text]",(t,{selector:e,compression:n,minFontSize:o,maxFontSize:r})=>{e&&Array.from(document.querySelectorAll(e)).forEach(i=>fu(i,{c:n,min:o,max:r}))});var du=()=>!!(/Android|webOS|Opera Mini|windows phone/i.test(navigator.userAgent)||/iPad|iPhone|iPod/.test(navigator.userAgent)&&!window.MSStream);function Mi(t){if(!Wt(t,"parallax"))return;if(du()){t.style.backgroundAttachment="scroll",t.style.backgroundSize="cover",t.style.backgroundPosition="center";return}let e=0;Wt(t,"bg-image")&&(e=.1),Wt(t,"bg-pattern")&&(e=.3);let n=uo(t).top,o=document.readyState==="complete",r=()=>{let{top:i}=uo(t);i+t.offsetHeight<window.scrollY||i>window.scrollY+window.innerHeight||(t.style.backgroundPosition=`50% ${Math.floor((n-window.scrollY)*e)}px`)};return o&&r(),[lu(r),Oi(()=>{n=uo(t).top}),co(()=>{o=!0,r()})]}J("section",Mi);J("content_band",Mi);J("column",(t,e)=>{!e.fade||fe(t.parentElement,()=>{t.style.opacity="1",t.style.transform="translate(0, 0)"},"65%")});J("classic_card",function(t){function e(m){m.target.tagName!=="A"&&uu(t,"flipped")}function n(){so(t,"flipped")}function o(){ao(t,"flipped")}let r=ht(t,"click",e),i=ht(t,"mouseenter",n),s=ht(t,"mouseleave",o),a;ht(t,"touchstart",()=>{a&&a(),a=ht(t,"touchend",e)}),ht(t,"touchmove",()=>{a&&a()}),Pi(t,"touchstart",()=>{r(),i(),s()});let c=t.querySelector(".x-card-inner"),u=t.querySelector(".x-face-outer.front"),d=u.querySelector(".x-face-content"),f=t.querySelector(".x-face-outer.back"),p=u.querySelector(".x-face-content"),l=m=>{let{borderTopWidth:g,borderBottomWidth:y}=getComputedStyle(f);return parseFloat(g)+parseFloat(y)};function h(){let m=Math.max(lo(d)+l(f),lo(p)+l(u));c.style.height=`${m}px`}return[co(h),Oi(h,!0)]});J("skill_bar",(t,e)=>{let n=t.querySelector(".bar");fe(t,()=>{Ii(0,{duration:750,update:o=>{n.style.width=`${o}%`}})(parseFloat(e.percent))},"95%")});J("recent_posts",function(t,{fade:e}){if(!e)return;let n=Array.from(t.querySelectorAll("a"));fe(t,()=>{n.forEach((o,r)=>{setTimeout(()=>{Ii(0,{duration:750,update:i=>{o.style.opacity=`${i}`}})(1)},r*90)}),setTimeout(()=>{so(t,"complete")},n*90+400)},"75%")});J("creative_cta",function(t,e){let n=t.querySelector(".graphic");n.style.transform="translate(-50%, -50%) scale(0)";let o=()=>{n.style.transform="translate(-50%, -50%) scale(0)",t.style.backgroundColor=e.bg_color};return o(),[ht(t,"mouseenter",()=>{t.style.backgroundColor=e.bg_color_hover,n.style.transform="translate(-50%, -50%) scale(1)"}),ht(t,"mouseleave",o)]});J("feature_box",function(t,e){e.child!==!0&&e.graphicAnimation!=="none"&&fe(t,()=>{setTimeout(()=>{de(t.querySelector(".x-feature-box-graphic-outer"),"animated "+e.graphicAnimation)},e.graphicAnimationDelay)},e.graphicAnimationOffset+"%")});J("feature_list",function(t,e){var n=fo(t.querySelector(".x-feature-box"),"data-x-params"),o=0;if(n.graphicAnimation!=="none"||n.connectorAnimation!=="none"){let r=Array.from(t.children);fe(t,function(){setTimeout(function(){r.forEach((i,s)=>{var c;if(n.graphicAnimation!=="none"&&de(i.querySelector(".x-feature-box-graphic-outer"),"animated "+n.graphicAnimation,o++,e.animationDelayBetween),n.connectorAnimation!=="none"){var a="animated "+n.connectorAnimation;let u=i.querySelector(".lower");u&&de(u,a,o,e.animationDelayBetween);let d=(c=r[s+1])==null?void 0:c.querySelector(".upper");d&&de(d,a,o,e.animationDelayBetween);let f=i.querySelector(".full");f&&de(f,a,o++,e.animationDelayBetween)}})},e.animationDelayInitial)},e.animationOffset+"%")}});J("tab_nav",function(t,{orientation:e}){e==="vertical"&&(t.style.minHeight=`${lo(t.nextElementSibling)}px`)});function de(t,e,n=0,o=0){let r=e.split(" ").map(i=>i.trim()).filter(i=>!!i);setTimeout(()=>{ao(t,"animated-hide"),r.forEach(i=>void so(t,i)),Pi(t,"animationend",()=>{r.forEach(i=>void ao(t,i))})},n*o)}window.csGoogleMapsClassic=function(){J("google_map",function(t,e){if(!window.google||!window.google.maps)return;function n(m){var g=[],y=[];Array.from(t.querySelectorAll(".x-google-map-marker")).forEach(function(v,w){var b=fo(v,"data-x-params"),C=new window.google.maps.Marker({map:m,position:new window.google.maps.LatLng(b.lat,b.lng),infoWindowIndex:w,icon:b.image});if(g[w]=C,b.markerInfo!==""){var x=new window.google.maps.InfoWindow({content:b.markerInfo,maxWidth:200});y[w]=x,b.startOpen&&x.open(m,C),window.google.maps.event.addListener(g[w],"click",function(){x.open(m,this)})}})}var o=t.querySelector(".x-google-map-inner"),r=e.lat,i=e.lng,s=new window.google.maps.LatLng(r,i),a=e.drag,c=parseInt(e.zoom),u=e.zoomControl,d=e.hue,f=[{featureType:"all",elementType:"all",stylers:[{hue:d||null}]},{featureType:"water",elementType:"all",stylers:[{hue:d||null},{saturation:0},{lightness:50}]},{featureType:"poi",elementType:"all",stylers:[{visibility:"off"}]}],p={scrollwheel:!1,draggable:a===!0,zoomControl:u===!0,disableDoubleClickZoom:!1,disableDefaultUI:!0,zoom:c,center:s,mapTypeId:window.google.maps.MapTypeId.ROADMAP},l=new window.google.maps.StyledMapType(f,{name:"Styled Map"}),h=new window.google.maps.Map(o,p);h.mapTypes.set("map_style",l),h.setMapTypeId("map_style"),n.call(this,h)})};co(()=>{let t=window.jQuery;if(!t)return;let e={animation:!0,placement:"top",template:'<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',trigger:"hover focus",title:"",container:!1},n={placement:"right",trigger:"click",content:"",template:'<div class="popover" role="tooltip"><div class="arrow"></div><h3 class="popover-title"></h3><div class="popover-content"></div></div>'};class o{constructor(i,{type:s="tooltip",...a}){this.type=s,this.options={...s==="popover"?n:e,...a},this.hoverState=null,this.$element=null,this.enter=()=>{this.hoverState="in",this.show()},this.leave=()=>{this.hoverState="out",this.hide()},this.toggle=()=>{this.tip().hasClass("in")?this.leave():this.enter()},this.el=i,this.$element=t(i),this.$body=t("body");for(var c=this.options.trigger.split(" "),u=c.length;u--;){var d=c[u];if(d=="click")this.$element.on("click."+this.type,!1,this.toggle);else{var f=d=="hover"?"mouseenter":"focusin",p=d=="hover"?"mouseleave":"focusout";this.$element.on(f+"."+this.type,!1,this.enter),this.$element.on(p+"."+this.type,!1,this.leave)}}(this.$element.attr("title")||typeof this.$element.attr("data-original-title")!="string")&&this.$element.attr("data-original-title",this.$element.attr("title")||"").attr("title","")}show(){var i=t.Event("show.bs."+this.type);if(this.getTitle()||this.getContent()){this.$element.trigger(i);var s=t.contains(document.documentElement,this.$element[0]);if(i.isDefaultPrevented()||!s)return;var a=this,c=this.tip(),u=this.getUID(this.type);this.setContent(),c.attr("id",u),this.$element.attr("aria-describedby",u),this.options.animation&&c.addClass("fade");var d=typeof this.options.placement=="function"?this.options.placement.call(this,c[0],this.$element[0]):this.options.placement,f=/\s?auto?\s?/i,p=f.test(d);p&&(d=d.replace(f,"")||"top"),c.detach().css({top:0,left:0,display:"block"}).addClass(d).data("bs."+this.type,this),this.options.container?c.appendTo(this.options.container):c.insertAfter(this.$element);var l=this.getElementPosition(this.el),h=c[0].offsetWidth,m=c[0].offsetHeight;if(p){var g=d,y=this.getElementPosition(this.el.parentElement);d=d=="bottom"&&l.top+l.height+m-y.scroll>y.height?"top":d=="top"&&l.top-y.scroll-m<0?"bottom":d=="right"&&l.right+h>y.width?"left":d=="left"&&l.left-h<y.left?"right":d,c.removeClass(g).addClass(d)}var v=this.getCalculatedOffset(d,l,h,m);this.applyPlacement(v,d);var w=function(){a.$element.trigger("shown.bs."+a.type),a.hoverState=null};this.$tip.hasClass("fade")?c.one("transitionend",w):w()}}applyPlacement(i,s){var a=this.tip(),c=a[0].offsetWidth,u=a[0].offsetHeight,d=parseInt(a.css("margin-top"),10),f=parseInt(a.css("margin-left"),10);isNaN(d)&&(d=0),isNaN(f)&&(f=0),i.top=i.top+d,i.left=i.left+f,t.offset.setOffset(a[0],t.extend({using:function(v){a.css({top:Math.round(v.top),left:Math.round(v.left)})}},i),0),a.addClass("in");var p=a[0].offsetWidth,l=a[0].offsetHeight;s=="top"&&l!=u&&(i.top=i.top+u-l);var h=this.getViewportAdjustedDelta(s,i,p,l);h.left?i.left+=h.left:i.top+=h.top;var m=h.left?h.left*2-c+p:h.top*2-u+l,g=h.left?"left":"top",y=h.left?"offsetWidth":"offsetHeight";a.offset(i),this.replaceArrow(m,a[0][y],g)}replaceArrow(i,s,a){this.arrow().css(a,i?50*(1-i/s)+"%":"")}setContent(){let i=this.tip(),s=this.getTitle();if(this.type==="popover"){let a=i.find(".popover-title");s?a.text(s):i.find(".popover-title").hide(),i.find(".popover-content").empty().text(this.getContent())}else i.find(".tooltip-inner").text(s);i.removeClass("fade in top bottom left right")}hide(){var i=this,s=this.tip(),a=t.Event("hide.bs."+this.type);this.$element.removeAttr("aria-describedby");function c(){i.hoverState!="in"&&s.detach(),i.$element.trigger("hidden.bs."+i.type)}if(this.$element.trigger(a),!a.isDefaultPrevented())return s.removeClass("in"),this.$tip.hasClass("fade")?s.one("transitionend",c):c(),this.hoverState=null,this}getContent(){return this.type!=="popover"?"":this.$element.attr("data-content")||this.getContentOption()}getViewportDimensions(){return{...window.document.body.getBoundingClientRect(),scroll:document.documentElement.scrollTop||document.body.scrollTop,width:window.innerWidth,height:window.innerHeight,top:0,left:0}}getElementPosition(i){let s=i.getBoundingClientRect();return{...s,scroll:i.scrollTop,width:i.offsetWidth,height:i.offsetHeight,top:s.top+(document.documentElement.scrollTop||document.body.scrollTop),left:s.left+(document.documentElement.scrollLeft||document.body.scrollLeft)}}getCalculatedOffset(i,s,a,c){return i=="bottom"?{top:s.top+s.height,left:s.left+s.width/2-a/2}:i=="top"?{top:s.top-c,left:s.left+s.width/2-a/2}:i=="left"?{top:s.top+s.height/2-c/2,left:s.left-a}:{top:s.top+s.height/2-c/2,left:s.left+s.width}}getViewportAdjustedDelta(i,s,a,c){var u={top:0,left:0},d=this.getViewportDimensions();if(/right|left/.test(i)){var f=s.top-d.scroll,p=s.top-d.scroll+c;f<d.top?u.top=d.top-f:p>d.top+d.height&&(u.top=d.top+d.height-p)}else{var l=s.left,h=s.left+a;l<d.left?u.left=d.left-l:h>d.width&&(u.left=d.left+d.width-h)}return u}getTitle(){return this.$element.attr("data-original-title")||this.getTitleOption()||this.type!=="popover"&&this.getContentOption()}getTitleOption(){return typeof this.options.title=="function"?this.options.title.call(this.$element[0]):this.options.title||""}getContentOption(){return typeof this.options.content=="function"?this.options.content.call(this.$element[0]):this.options.content||""}getUID(i){do i+=~~(Math.random()*1e6);while(document.getElementById(i));return i}tip(){return this.$tip=this.$tip||t(this.options.template)}arrow(){return this.$arrow||(this.$arrow=this.tip().find(this.type==="popover"?".arrow":".tooltip-arrow")),this.$arrow}validate(){this.$element[0].parentNode||(this.hide(),this.$element=null,this.options=null)}}J("extra",(r,i)=>void new o(r,i))});var Rp=window.csGlobal;})();
